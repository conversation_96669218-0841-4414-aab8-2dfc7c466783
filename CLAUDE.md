# TitleSync Development Guide

## Commands
- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm lint` - Run Biome linter and fix issues
- `pnpm typecheck` - Check TypeScript types (install if needed)

## Code Style Guidelines
- **Formatting**: Uses Biome with 2-space indentation and double quotes
- **Imports**: Organize imports alphabetically, group by external/internal
- **Types**: Use TypeScript types for all variables/functions
- **Components**: Use function components with explicit return types
- **State Management**: Prefer React hooks, use React.useState/useEffect
- **Error Handling**: Handle async errors with try/catch or error states
- **Forms**: Use react-hook-form with effectTsResolver from Effect.ts
- **Naming**: camelCase for variables/functions, PascalCase for components/types
- **UI Components**: Use shadcn/ui components from ~/components/ui
- **API Pattern**: Effect.ts for API schemas and type definitions