{"version": "6", "dialect": "sqlite", "id": "3382e15c-84ca-482a-9c51-1636194753d2", "prevId": "7fb4a4f5-820c-4136-adf4-3b0506412cd5", "tables": {"homeowner_associations": {"name": "homeowner_associations", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "uid": {"name": "uid", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "organizations": {"name": "organizations", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "uid": {"name": "uid", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "clerk_id": {"name": "clerk_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"organizations_clerk_id_unique": {"name": "organizations_clerk_id_unique", "columns": ["clerk_id"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "settlements": {"name": "settlements", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "uid": {"name": "uid", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "property_address": {"name": "property_address", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "unit": {"name": "unit", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "hoa_name": {"name": "hoa_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "buyer_email": {"name": "buyer_email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "warranty_deed_key": {"name": "warranty_deed_key", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_status": {"name": "payment_status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'pending'"}, "seller_expenses": {"name": "seller_expenses", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "buyer_expenses": {"name": "buyer_expenses", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "total_amount": {"name": "total_amount", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "uid": {"name": "uid", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "clerk_id": {"name": "clerk_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"users_clerk_id_unique": {"name": "users_clerk_id_unique", "columns": ["clerk_id"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}