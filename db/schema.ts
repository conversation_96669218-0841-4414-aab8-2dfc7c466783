import { int, integer, sqliteTable, text } from "drizzle-orm/sqlite-core";

export const usersTable = sqliteTable("users", {
  id: int().primary<PERSON>ey({ autoIncrement: true }),
  uid: text().notNull(),
  clerk_id: text().notNull().unique(),
  name: text().notNull(),
  email: text().notNull(),
});

export const orgsTable = sqliteTable("organizations", {
  id: int().primary<PERSON>ey({ autoIncrement: true }),
  uid: text().notNull(),
  clerk_id: text().notNull().unique(),
  name: text().notNull(),
  slug: text().notNull(),
});

export const settlementsTable = sqliteTable("settlements", {
  id: int().primaryKey({ autoIncrement: true }),
  uid: text().notNull(),
  name: text(), // Made optional
  organization_id: text().notNull(),
  property_address: text().notNull(),
  unit: text(),
  hoa_name: text(),
  buyer_email: text(),
  warranty_deed_key: text(), // store the key to the file in r2
  amount: integer(),
  payment_status: text().notNull().default("pending"),
  seller_expenses: text({ mode: "json" }), // list of expenses
  buyer_expenses: text({ mode: "json" }), // list of expenses
  total_amount: integer(),
  notes: text(), // Special notes for the settlement
  created_at: text().notNull(),
  updated_at: text().notNull(),
});

export const homeownerAssociationsTable = sqliteTable(
  "homeowner_associations",
  {
    id: int().primaryKey({ autoIncrement: true }),
    uid: text().notNull(),
    name: text().notNull(),
    created_at: text().notNull(),
    updated_at: text().notNull(),
  },
);
