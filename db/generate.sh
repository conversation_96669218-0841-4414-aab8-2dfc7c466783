#!/bin/sh

drizzle-kit generate

# Copy the migration file
cp ./db/drizzle/migrations.js ./db/drizzle/migrations-patch.ts 

# Replace the imports with ?raw sql imports to work with vite bundling
sed -i.bak 's/.sql/.sql?raw/g' ./db/drizzle/migrations-patch.ts 

# Add comment to the top of the file
echo "// Autogenerated by db/generate.sh\n" > ./db/drizzle/migrations-patch.ts.tmp
cat ./db/drizzle/migrations-patch.ts >> ./db/drizzle/migrations-patch.ts.tmp 
mv ./db/drizzle/migrations-patch.ts.tmp ./db/drizzle/migrations-patch.ts

rm ./db/drizzle/migrations-patch.ts.bak