{"name": "titlesync", "private": true, "type": "module", "scripts": {"build": "react-router build", "deploy": "npm run build && wrangler deploy", "dev": "react-router dev --host", "dev:alt": "react-router dev --host --port 3000", "preview": "vite preview", "start": "wrangler dev", "typegen": "react-router typegen && wrangler types", "typecheck": "tsc -b", "generate": "./db/generate.sh", "format": "biome format --write app db workers", "lint": "biome lint --write app db workers"}, "dependencies": {"@ai-sdk/openai": "^1.3.3", "@ai-sdk/react": "^1.2.2", "@ai-sdk/ui-utils": "^1.2.1", "@biomejs/biome": "^1.9.4", "@clerk/backend": "^1.26.0", "@clerk/react-router": "^1.1.11", "@effect/platform": "^0.80.1", "@hookform/resolvers": "^4.1.3", "@monaco-editor/react": "^4.7.0", "@outerbase/browsable-durable-object": "^0.1.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@remix-run/react": "^2.16.5", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@tanstack/react-query": "^5.69.0", "@tanstack/react-table": "^8.21.2", "agents": "^0.0.43", "ai": "^4.2.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "drizzle-kit": "^0.30.6", "drizzle-orm": "^0.41.0", "effect": "^3.14.1", "embla-carousel-react": "^8.5.2", "input-otp": "^1.4.2", "isbot": "^5.1.17", "jazz-browser-media-images": "^0.11.6", "jazz-react": "0.11.6", "jazz-tools": "0.11.6", "lucide-react": "^0.483.0", "micromark": "3.2.0", "monaco-editor": "^0.52.2", "monaco-yaml": "^5.3.1", "motion": "^12.6.2", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-markdown": "^9.0.3", "react-resizable-panels": "^2.1.7", "react-router": "^7.4.0", "react-router-dom": "^7.5.2", "recharts": "^2.15.1", "rehype-raw": "^7.0.0", "remix-utils": "^8.5.0", "sonner": "^2.0.1", "stripe": "^18.1.1", "tailwind-merge": "^3.0.2", "typeid-js": "^1.2.0", "vaul": "^1.1.2", "yaml": "2.6.1"}, "devDependencies": {"@cloudflare/vite-plugin": "^0.1.15", "@cloudflare/workers-types": "^4.20250321.0", "@react-router/dev": "^7.4.0", "@tailwindcss/vite": "^4.0.15", "@types/node": "^20.17.27", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "tailwindcss": "^4.0.15", "tw-animate-css": "^1.2.4", "typescript": "^5.8.2", "vite": "^6.2.3", "vite-tsconfig-paths": "^5.1.4", "wrangler": "^3.114.2"}, "packageManager": "pnpm@9.15.4+sha512.b2dc20e2fc72b3e18848459b37359a32064663e5627a51e4c74b2c29dd8e8e0491483c3abb40789cfd578bf362fb6ba8261b05f0387d76792ed6e23ea3b1b6a0", "pnpm": {"patchedDependencies": {"msgpackr": "patches/msgpackr.patch"}}}