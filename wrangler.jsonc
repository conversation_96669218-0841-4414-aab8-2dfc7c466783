{"$schema": "node_modules/wrangler/config-schema.json", "name": "titlesync", "compatibility_date": "2025-02-24", "compatibility_flags": ["nodejs_compat"], "main": "./workers/app.ts", "assets": {}, "vars": {"VALUE_FROM_CLOUDFLARE": "Hello from Cloudflare"}, "durable_objects": {"bindings": [{"name": "MyDurableObject", "class_name": "MyDurableObject"}]}, "migrations": [{"tag": "v2", "new_sqlite_classes": ["MyDurableObject"]}], "r2_buckets": [{"binding": "BUCKET", "bucket_name": "metacrdt-web-demo"}]}