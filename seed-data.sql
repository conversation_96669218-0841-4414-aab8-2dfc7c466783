-- SQL to insert test settlements for Clayton Property Management
-- Replace CLAYTON_ORG_ID with the actual organization ID
-- Run this SQL in the Cloudflare D1 console or using wrangler

-- Settlement 1: [TEST] Settlement for 123 Main Street
INSERT INTO settlements (
  uid,
  name,
  organization_id,
  property_address,
  unit,
  hoa_name,
  buyer_email,
  warranty_deed_key,
  amount,
  payment_status,
  seller_expenses,
  buyer_expenses,
  total_amount,
  created_at,
  updated_at
) VALUES (
  'settlement_01jsn6vht1fk7vxgdc2bd04r1n',
  '[TEST] Settlement for 123 Main Street',
  'org_2YXnZXXXXXXXXXXXXXXXXXXX',
  '123 Main Street, Austin, TX 78701',
  'Unit 818',
  'Lakeside Village HOA',
  '<EMAIL>',
  NULL,
  NULL,
  'pending',
  '[{"category":"working-capital","amount":216},{"category":"other","amount":197,"subcategory":"Administrative fee"},{"category":"special-assessment","amount":759},{"category":"special-assessment","amount":737}]',
  '[{"category":"dues","amount":872,"month":"August","year":"2025"}]',
  2781,
  '2024-12-15T21:21:18.169Z',
  '2024-12-15T21:21:18.169Z'
);

-- Settlement 2: [TEST] Settlement for 456 Oak Avenue
INSERT INTO settlements (
  uid,
  name,
  organization_id,
  property_address,
  unit,
  hoa_name,
  buyer_email,
  warranty_deed_key,
  amount,
  payment_status,
  seller_expenses,
  buyer_expenses,
  total_amount,
  created_at,
  updated_at
) VALUES (
  'settlement_01jsn6vht2fk7vxgdm7fszxkfy',
  '[TEST] Settlement for 456 Oak Avenue',
  'org_2YXnZXXXXXXXXXXXXXXXXXXX',
  '456 Oak Avenue, Austin, TX 78702',
  NULL,
  'Willow Creek Estates',
  '<EMAIL>',
  NULL,
  NULL,
  'pending',
  '[{"category":"transfer-fee","amount":741},{"category":"working-capital","amount":602},{"category":"transfer-fee","amount":74},{"category":"move-in-out-fee","amount":938}]',
  '[{"category":"working-capital","amount":379}]',
  2734,
  '2025-01-02T02:39:08.253Z',
  '2025-01-02T02:39:08.253Z'
);

-- Settlement 3: [TEST] Settlement for 789 Pine Boulevard
INSERT INTO settlements (
  uid,
  name,
  organization_id,
  property_address,
  unit,
  hoa_name,
  buyer_email,
  warranty_deed_key,
  amount,
  payment_status,
  seller_expenses,
  buyer_expenses,
  total_amount,
  created_at,
  updated_at
) VALUES (
  'settlement_01jsn6vht2fk7vxgdvgd3c710n',
  '[TEST] Settlement for 789 Pine Boulevard',
  'org_2YXnZXXXXXXXXXXXXXXXXXXX',
  '789 Pine Boulevard, Austin, TX 78703',
  NULL,
  'Sunset Hills Association',
  '<EMAIL>',
  NULL,
  NULL,
  'overdue',
  '[{"category":"working-capital","amount":750},{"category":"move-in-out-fee","amount":758},{"category":"transfer-fee","amount":458}]',
  '[{"category":"transfer-fee","amount":320}]',
  2286,
  '2025-02-24T04:25:40.420Z',
  '2025-02-24T04:25:40.420Z'
);

-- Settlement 4: [TEST] Settlement for 321 Maple Drive
INSERT INTO settlements (
  uid,
  name,
  organization_id,
  property_address,
  unit,
  hoa_name,
  buyer_email,
  warranty_deed_key,
  amount,
  payment_status,
  seller_expenses,
  buyer_expenses,
  total_amount,
  created_at,
  updated_at
) VALUES (
  'settlement_01jsn6vht2fk7vxge5g9zc4n6y',
  '[TEST] Settlement for 321 Maple Drive',
  'org_2YXnZXXXXXXXXXXXXXXXXXXX',
  '321 Maple Drive, Austin, TX 78704',
  'Unit 981',
  'Oakridge Community Association',
  '<EMAIL>',
  NULL,
  NULL,
  'processing',
  '[{"category":"move-in-out-fee","amount":246},{"category":"move-in-out-fee","amount":896}]',
  '[{"category":"dues","amount":590,"month":"February","year":"2025"},{"category":"dues","amount":262,"month":"June","year":"2025"}]',
  1994,
  '2024-12-25T01:32:08.748Z',
  '2024-12-25T01:32:08.748Z'
);

-- Settlement 5: [TEST] Settlement for 654 Cedar Lane
INSERT INTO settlements (
  uid,
  name,
  organization_id,
  property_address,
  unit,
  hoa_name,
  buyer_email,
  warranty_deed_key,
  amount,
  payment_status,
  seller_expenses,
  buyer_expenses,
  total_amount,
  created_at,
  updated_at
) VALUES (
  'settlement_01jsn6vht2fk7vxgefp0k60b2n',
  '[TEST] Settlement for 654 Cedar Lane',
  'org_2YXnZXXXXXXXXXXXXXXXXXXX',
  '654 Cedar Lane, Austin, TX 78705',
  'Unit 950',
  'Willow Creek Estates',
  '<EMAIL>',
  NULL,
  NULL,
  'overdue',
  '[{"category":"special-assessment","amount":113},{"category":"special-assessment","amount":945}]',
  '[{"category":"transfer-fee","amount":530}]',
  1588,
  '2024-12-01T23:36:56.300Z',
  '2024-12-01T23:36:56.300Z'
);

-- Settlement 6: [TEST] Settlement for 987 Elm Street
INSERT INTO settlements (
  uid,
  name,
  organization_id,
  property_address,
  unit,
  hoa_name,
  buyer_email,
  warranty_deed_key,
  amount,
  payment_status,
  seller_expenses,
  buyer_expenses,
  total_amount,
  created_at,
  updated_at
) VALUES (
  'settlement_01jsn6vht2fk7vxgehgtezheqq',
  '[TEST] Settlement for 987 Elm Street',
  'org_2YXnZXXXXXXXXXXXXXXXXXXX',
  '987 Elm Street, Austin, TX 78712',
  NULL,
  'Evergreen Valley HOA',
  '<EMAIL>',
  NULL,
  NULL,
  'pending',
  '[{"category":"other","amount":971,"subcategory":"Processing fee"},{"category":"move-in-out-fee","amount":68}]',
  '[{"category":"dues","amount":858,"month":"August","year":"2025"},{"category":"move-in-out-fee","amount":913}]',
  2810,
  '2024-12-06T00:37:10.701Z',
  '2024-12-06T00:37:10.701Z'
);

-- Settlement 7: [TEST] Settlement for 246 Birch Road
INSERT INTO settlements (
  uid,
  name,
  organization_id,
  property_address,
  unit,
  hoa_name,
  buyer_email,
  warranty_deed_key,
  amount,
  payment_status,
  seller_expenses,
  buyer_expenses,
  total_amount,
  created_at,
  updated_at
) VALUES (
  'settlement_01jsn6vht3fk7vxgettnxpwkqt',
  '[TEST] Settlement for 246 Birch Road',
  'org_2YXnZXXXXXXXXXXXXXXXXXXX',
  '246 Birch Road, Austin, TX 78721',
  NULL,
  'Meadowbrook Community',
  '<EMAIL>',
  NULL,
  NULL,
  'processing',
  '[{"category":"move-in-out-fee","amount":826},{"category":"move-in-out-fee","amount":202},{"category":"dues","amount":432,"month":"August","year":"2025"}]',
  '[{"category":"working-capital","amount":891},{"category":"other","amount":316,"subcategory":"Cleaning fee"}]',
  2667,
  '2025-02-08T18:01:57.313Z',
  '2025-02-08T18:01:57.313Z'
);

-- Settlement 8: [TEST] Settlement for 135 Willow Way
INSERT INTO settlements (
  uid,
  name,
  organization_id,
  property_address,
  unit,
  hoa_name,
  buyer_email,
  warranty_deed_key,
  amount,
  payment_status,
  seller_expenses,
  buyer_expenses,
  total_amount,
  created_at,
  updated_at
) VALUES (
  'settlement_01jsn6vht3fk7vxgf588x285g9',
  '[TEST] Settlement for 135 Willow Way',
  'org_2YXnZXXXXXXXXXXXXXXXXXXX',
  '135 Willow Way, Austin, TX 78722',
  NULL,
  'Riverside Estates HOA',
  '<EMAIL>',
  NULL,
  NULL,
  'pending',
  '[{"category":"working-capital","amount":269},{"category":"transfer-fee","amount":405},{"category":"other","amount":123,"subcategory":"Administrative fee"}]',
  '[{"category":"dues","amount":220,"month":"February","year":"2025"}]',
  1017,
  '2024-12-31T04:32:37.750Z',
  '2024-12-31T04:32:37.750Z'
);

-- Settlement 9: [TEST] Settlement for 864 Spruce Court
INSERT INTO settlements (
  uid,
  name,
  organization_id,
  property_address,
  unit,
  hoa_name,
  buyer_email,
  warranty_deed_key,
  amount,
  payment_status,
  seller_expenses,
  buyer_expenses,
  total_amount,
  created_at,
  updated_at
) VALUES (
  'settlement_01jsn6vht3fk7vxgff54dy8dms',
  '[TEST] Settlement for 864 Spruce Court',
  'org_2YXnZXXXXXXXXXXXXXXXXXXX',
  '864 Spruce Court, Austin, TX 78723',
  NULL,
  'Cedar Heights Community',
  '<EMAIL>',
  NULL,
  NULL,
  'processing',
  '[{"category":"move-in-out-fee","amount":520},{"category":"other","amount":351,"subcategory":"Administrative fee"},{"category":"other","amount":528,"subcategory":"Cleaning fee"}]',
  '[{"category":"special-assessment","amount":674},{"category":"transfer-fee","amount":446}]',
  2519,
  '2024-10-29T14:20:05.618Z',
  '2024-10-29T14:20:05.618Z'
);

-- Settlement 10: [TEST] Settlement for 579 Redwood Circle
INSERT INTO settlements (
  uid,
  name,
  organization_id,
  property_address,
  unit,
  hoa_name,
  buyer_email,
  warranty_deed_key,
  amount,
  payment_status,
  seller_expenses,
  buyer_expenses,
  total_amount,
  created_at,
  updated_at
) VALUES (
  'settlement_01jsn6vht3fk7vxgfmjjanhybg',
  '[TEST] Settlement for 579 Redwood Circle',
  'org_2YXnZXXXXXXXXXXXXXXXXXXX',
  '579 Redwood Circle, Austin, TX 78724',
  'Unit 845',
  'Meadowbrook Community',
  '<EMAIL>',
  NULL,
  NULL,
  'pending',
  '[{"category":"move-in-out-fee","amount":271},{"category":"special-assessment","amount":277},{"category":"working-capital","amount":706},{"category":"special-assessment","amount":689}]',
  '[{"category":"working-capital","amount":676}]',
  2619,
  '2025-02-04T07:19:03.536Z',
  '2025-02-04T07:19:03.536Z'
);

-- End of SQL
