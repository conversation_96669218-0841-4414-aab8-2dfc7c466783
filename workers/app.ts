import { studio } from "@outerbase/browsable-durable-object";
import { createRe<PERSON>Hand<PERSON> } from "react-router";
import { MyDurableObject } from "~/agents/account-object";
import { typeid } from "typeid-js";

declare global {
  interface CloudflareEnvironment extends Env {}
}

declare module "react-router" {
  export interface AppLoadContext {
    cloudflare: {
      env: CloudflareEnvironment;
      ctx: ExecutionContext;
    };
  }
}

const requestHandler = createRequestHandler(
  () => import("virtual:react-router/server-build"),
  import.meta.env.MODE,
);

export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);

    if (url.pathname === "/studio") {
      return await studio(request, env.MyDurableObject, {
        basicAuth: {
          username: "admin",
          password: "password",
        },
      });
    }

    // Handle file download endpoint
    if (url.pathname.startsWith("/uploads/") && request.method === "GET") {
      try {
        // Extract the file ID from the URL
        const fileId = url.pathname.replace("/uploads/", "");
        if (!fileId) {
          return new Response("File ID not provided", { status: 400 });
        }

        // Construct the key for R2
        const key = `uploads/${fileId}`;

        // Get the file from R2
        const object = await env.BUCKET.get(key);

        // Check if the file exists
        if (!object) {
          return new Response("File not found", { status: 404 });
        }

        // Get the file data
        const data = await object.arrayBuffer();

        // Determine the content type
        const contentType =
          object.httpMetadata?.contentType || "application/octet-stream";

        // Return the file
        return new Response(data, {
          headers: {
            "Content-Type": contentType,
            "Content-Disposition": `inline; filename="${fileId}"`,
            "Cache-Control": "public, max-age=31536000", // Cache for 1 year
          },
        });
      } catch (error) {
        console.error("Error retrieving file:", error);
        return new Response(
          `Error retrieving file: ${error instanceof Error ? error.message : "Unknown error"}`,
          { status: 500 },
        );
      }
    }

    // Handle simple upload endpoint
    if (url.pathname === "/upload" && request.method === "POST") {
      try {
        // Generate a unique key for the file
        const key = `uploads/${typeid("file").toString()}`;

        // Get the file from the request
        const formData = await request.formData();
        const file = formData.get("file");

        if (!file || !(file instanceof File)) {
          return new Response(JSON.stringify({ error: "No file provided" }), {
            status: 400,
            headers: { "Content-Type": "application/json" },
          });
        }

        // Upload the file to R2
        await env.BUCKET.put(key, file, {
          httpMetadata: { contentType: file.type },
        });

        // Return the key
        return new Response(JSON.stringify({ success: true, key }), {
          headers: { "Content-Type": "application/json" },
        });
      } catch (error) {
        console.error("Error uploading file:", error);
        return new Response(
          JSON.stringify({
            error: error instanceof Error ? error.message : "Unknown error",
          }),
          {
            status: 500,
            headers: { "Content-Type": "application/json" },
          },
        );
      }
    }

    // Handle API requests
    if (url.pathname.startsWith("/api")) {
      const id = env.MyDurableObject.idFromName("db");
      const obj = env.MyDurableObject.get(id);
      return obj.fetch(request);
    }

    // Handle all other requests via React Router / Vite
    return requestHandler(request, {
      cloudflare: { env, ctx },
    });
  },
} satisfies ExportedHandler<CloudflareEnvironment>;

export { MyDurableObject };
