import {
  drizzle,
  type DrizzleSqliteDODatabase,
} from "drizzle-orm/durable-sqlite";
import { migrate } from "drizzle-orm/durable-sqlite/migrator";
import migrations from "../../db/drizzle/migrations-patch";

import { Browsable } from "@outerbase/browsable-durable-object";
import { DurableObject } from "cloudflare:workers";
import { Context } from "effect";
import { OriginalRequest } from "~/api/cloudflare";
import { Db } from "~/api/db";

@Browsable()
export class MyDurableObject extends DurableObject<Env> {
  storage: DurableObjectStorage;
  db: DrizzleSqliteDODatabase;

  constructor(ctx: DurableObjectState, env: Env) {
    super(ctx, env);
    this.storage = ctx.storage;
    this.db = drizzle(this.storage, { logger: false });

    // this.handler = new BrowsableHandler(this.storage.sql);

    ctx.blockConcurrencyWhile(async () => {
      try {
        await this.migrate();
      } catch (e) {
        console.error("Error migrating database:", e);
      }
    });
  }

  async fetch(request: Request) {
    const { makeHandler } = await import("~/api/api-live.server");

    const handler = makeHandler({
      env: this.env,
      db: this.db,
    });

    const context = Context.empty().pipe(
      Context.add(OriginalRequest, request),
      Context.add(Db, this.db),
    );

    return handler(request, context);
  }

  async clearData() {
    this.storage.deleteAll();
  }

  async migrate() {
    
    await migrate(this.db, migrations);
  }
}
