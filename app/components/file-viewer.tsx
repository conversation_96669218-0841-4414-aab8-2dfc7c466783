import { FileText, ExternalLink, Download, FileIcon } from 'lucide-react';
import { Button } from '~/components/ui/button';
import { getFileUrl, isImageFile, isPdfFile, extractFileId } from '~/utils/file-utils';

interface FileViewerProps {
  fileKey: string | null | undefined;
  className?: string;
  label?: string;
  showFileName?: boolean;
  maxFileNameLength?: number;
}

export function FileViewer({
  fileKey,
  className = '',
  label = 'Uploaded File',
  showFileName = true,
  maxFileNameLength = 20
}: FileViewerProps) {
  // Get the URL for the file
  const fileUrl = getFileUrl(fileKey);

  if (!fileUrl) {
    return <div className="text-sm text-gray-500">No file available</div>;
  }

  // Get file name from the key
  const fileName = extractFileId(fileKey) || 'file';

  // Create a truncated version of the file name for display
  const truncateFileName = (name: string, maxLength = maxFileNameLength) => {
    if (name.length <= maxLength) return name;

    // Split the name to preserve the extension
    const lastDotIndex = name.lastIndexOf('.');
    if (lastDotIndex === -1) {
      // No extension, simple truncation
      return `${name.substring(0, maxLength - 3)}...`;
    }

    const extension = name.substring(lastDotIndex);
    const nameWithoutExt = name.substring(0, lastDotIndex);

    // Calculate how much of the name we can show
    const availableChars = maxLength - extension.length - 3; // 3 for the ellipsis

    if (availableChars <= 0) {
      // Extension is too long, truncate the whole thing
      return `${name.substring(0, maxLength - 3)}...`;
    }

    // Return the truncated name with extension
    return `${nameWithoutExt.substring(0, availableChars)}...${extension}`;
  };

  const displayFileName = truncateFileName(fileName);

  // Determine the type of file
  const isImage = isImageFile(fileKey);
  const isPdf = isPdfFile(fileKey);

  // Choose the appropriate icon based on file type
  const FileTypeIcon = isPdf ? FileText : isImage ? FileIcon : FileText;

  const openInNewTab = () => {
    if (fileUrl) {
      window.open(fileUrl, '_blank');
    }
  };

  const downloadFile = () => {
    if (fileUrl) {
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 rounded-md gap-4 ${className}`}>
      <div className="flex items-center gap-3">
        <div className="flex-shrink-0 p-2 bg-gray-100 rounded-md">
          <FileTypeIcon className="h-5 w-5 text-gray-500" />
        </div>
        <div className="min-w-0 flex-1"> {/* min-w-0 allows truncation to work properly */}
          <p className="text-sm font-medium">{label}</p>
          {showFileName && (
            <p
              className="text-xs text-muted-foreground truncate"
              title={fileName} // Show full name on hover
            >
              {displayFileName}
            </p>
          )}
        </div>
      </div>

      <div className="flex gap-3 mt-3 sm:mt-0">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={openInNewTab}
          title="View in new tab"
          className="flex-1 sm:flex-none shadow-sm"
        >
          <ExternalLink className="w-4 h-4 mr-1.5" />
          View
        </Button>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={downloadFile}
          title="Download file"
          className="flex-1 sm:flex-none shadow-sm"
        >
          <Download className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}
