import { Check<PERSON>ircle<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Share2, Building2 } from "lucide-react";
import { But<PERSON> } from "~/components/ui/button";
import { useState } from "react";
import { ResourceId } from "~/components/resource-id";
import { Link } from "react-router";

interface SuccessMessageProps {
  title?: string;
  message: string;
  resourceId?: string;
  resourceLabel?: string;
  returnUrl?: string;
  returnLabel?: string;
  onShare?: () => void;
  className?: string;
  propertyAddress?: string;
}

export function SuccessMessage({
  title = "Success!",
  message,
  resourceId,
  resourceLabel = "ID",
  returnUrl = "/",
  returnLabel = "Return Home",
  onShare,
  className,
  propertyAddress,
}: SuccessMessageProps) {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = () => {
    if (!resourceId) return;

    navigator.clipboard.writeText(resourceId).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };

  return (
    <div className="flex flex-col items-center justify-center w-full max-w-sm sm:max-w-md mx-auto bg-white rounded-2xl shadow-lg border border-gray-200 p-6">
      <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
        <CheckCircle2 className="h-10 w-10 text-green-600" />
      </div>
      <h1 className="text-2xl font-bold text-gray-800 text-center mb-2">{title}</h1>
      <p className="text-base text-gray-600 text-center mb-6">{message}</p>

      <div className="w-full space-y-4">
        {propertyAddress && (
          <div className="rounded-lg border border-gray-200 p-4 bg-gray-50">
            <div className="mb-2 text-sm font-medium text-gray-600 flex items-center">
              <Building2 className="h-4 w-4 mr-1 text-blue-600" />
              <span>Property Address</span>
            </div>
            <div className="text-base font-medium text-gray-800">
              {propertyAddress}
            </div>
          </div>
        )}

        {resourceId && (
          <div className="rounded-lg border border-gray-200 p-4 bg-gray-50">
            <div className="mb-2 text-sm font-medium text-gray-600 flex items-center">
              <CheckCircle2 className="h-4 w-4 mr-1 text-green-600" />
              <span>{resourceLabel}</span>
            </div>
            <div className="flex items-center justify-between gap-2">
              <ResourceId
                id={resourceId}
                truncate={false}
                className="flex-grow"
              />
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-1 border-gray-200 hover:bg-gray-50"
                onClick={copyToClipboard}
              >
                {copied ? "Copied!" : "Copy"}
                {copied ? (
                  <CheckCircle2 className="h-4 w-4 text-green-500" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        )}

        <div className="flex flex-col gap-2 pt-4 mt-2">
          <Button
            variant="default"
            className="w-full bg-blue-600 hover:bg-blue-700 py-4 text-base rounded-lg shadow-sm transition-all flex items-center justify-center gap-1"
            asChild
          >
            <Link to={returnUrl}>
              <ArrowLeft className="h-4 w-4 mr-1" />
              {returnLabel}
            </Link>
          </Button>

          {onShare && (
            <Button
              variant="outline"
              className="w-full border-gray-200 hover:bg-gray-50 py-4 text-base rounded-lg"
              onClick={onShare}
            >
              <Share2 className="mr-2 h-4 w-4" />
              Share Details
            </Button>
          )}

          <div className="w-full text-center mt-4 pt-4 border-t border-gray-100">
            <p className="text-xs text-muted-foreground opacity-70">
              © 2025 TitleSync
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
