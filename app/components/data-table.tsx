import {
  type ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[] | readonly TData[];
}

export function DataTable<TData, TValue>({
  columns,
  data,
}: DataTableProps<TData, TValue>) {
  const table = useReactTable({
    data: data as TData[],
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div className="border border-gray-200 bg-background shadow-sm overflow-hidden">
      <div className="w-full overflow-x-auto">
        <Table className="min-w-full rounded-none !rounded-none">
          <TableHeader className="rounded-none !rounded-none">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className="bg-gray-100 border-b border-gray-200 rounded-none !rounded-none">
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} className="py-3 px-3 text-sm font-semibold text-gray-700 uppercase tracking-wider whitespace-nowrap rounded-none !rounded-none border-radius-0">
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  className="border-b border-gray-100 hover:bg-blue-50/30 transition-colors"
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="py-3 px-3">
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-60 text-center">
                  <div className="flex flex-col items-center justify-center py-12 sm:py-16 px-4 sm:px-6">
                    <div className="rounded-full bg-gray-100 p-4 mb-5 shadow-sm">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-7 w-7 text-gray-600">
                        <title>Icon</title>
                        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
                      </svg>
                    </div>
                    <p className="text-xl font-medium text-gray-900 mb-3">No results found</p>
                    <p className="text-sm text-gray-500 max-w-md mx-auto text-center mb-6">
                      {window.location.search ? (
                        <>No settlements match your current search criteria. Try adjusting your filters.</>
                      ) : (
                        <>You haven't created any settlements yet. Get started by initiating your first settlement.</>
                      )}
                    </p>
                    <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
                      <button
                        type="button"
                        onClick={() => (document.querySelector('[aria-label="Initiate Settlement"]') as HTMLAnchorElement)?.click()}
                        className="inline-flex items-center justify-center gap-2 px-4 py-2 bg-sky-600 hover:bg-sky-700 text-white shadow-sm transition-colors"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <title>Icon</title>
                          <path d="M12 5v14M5 12h14" />
                        </svg>
                        <span>Initiate Settlement</span>
                      </button>
                      {window.location.search && (
                        <button
                          type="button"
                          onClick={() => window.location.replace(window.location.pathname)}
                          className="inline-flex items-center justify-center gap-2 px-4 py-2 bg-white border border-gray-200 text-gray-700 shadow-sm hover:bg-gray-50 transition-colors"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <title>Icon</title>
                            <path d="M3 3v18h18" />
                            <path d="m3 17 8-8 4 4 8-8" />
                          </svg>
                          <span>Clear Filters</span>
                        </button>
                      )}
                    </div>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
