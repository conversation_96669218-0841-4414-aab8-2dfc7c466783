import { <PERSON> } from "react-router";
import { ArrowLeft } from "lucide-react";
import { But<PERSON> } from "~/components/ui/button";
import { Logo } from "~/components/logo";

export function TitleCompanyHeader() {
  return (
    <header className="border-b border-border bg-background shadow-md">
      <div className="container max-w-7xl mx-auto flex h-16 items-center justify-between px-6 md:px-8">
        <div className="flex items-center">
          <Link to="/" className="flex items-center py-2">
            <Logo />
          </Link>
        </div>
        <div className="flex items-center">
          <Button type="button" variant="outline" size="sm" asChild>
            <Link to="/" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              <span>Back to Home</span>
            </Link>
          </Button>
        </div>
      </div>
    </header>
  );
}
