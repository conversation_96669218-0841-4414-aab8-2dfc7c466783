import { useState } from "react";
import { <PERSON> } from "react-router";
import { Building2, FileBadge, Menu, X, User, LogOut, Users, Building } from "lucide-react";
import {
  OrganizationSwitcher,
  SignedIn,
  SignedOut,
  SignInButton,
  useOrganization,
  UserButton,
  useUser,
  useAuth,
} from "@clerk/react-router";
import { Button } from "~/components/ui/button";
import { Logo } from "~/components/logo";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetClose,
} from "~/components/ui/sheet";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Separator } from "~/components/ui/separator";

export function Header() {
  const organization = useOrganization();
  const { user } = useUser();
  const { signOut } = useAuth();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <header className="border-b border-border bg-background shadow-md">
      <div className="container max-w-7xl mx-auto flex h-16 items-center justify-between px-6 md:px-8">
        <div className="flex items-center">
          <Link to="/" className="flex items-center py-2">
            <Logo />
          </Link>
        </div>

        <div className="flex items-center">
          <SignedIn>
            <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="h-10 w-10 rounded-full">
                  <div className="flex flex-col space-y-1.5">
                    <div className="w-5 h-[2px] bg-gray-700 rounded-full" />
                    <div className="w-5 h-[2px] bg-gray-700 rounded-full" />
                    <div className="w-5 h-[2px] bg-gray-700 rounded-full" />
                  </div>
                  <span className="sr-only">Toggle menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent className="w-[300px] sm:w-[350px] p-0">
                <SheetHeader className="px-6 py-5 border-b">
                  <SheetTitle className="text-xl font-semibold">Menu</SheetTitle>
                </SheetHeader>

                <div className="py-4">
                  {/* User Account Section */}
                  <div className="mb-4">
                    <h3 className="text-sm font-medium mb-3 px-6">User Account</h3>
                    <div className="flex items-center px-6 py-2">
                      <div className="flex items-center justify-center h-9 w-9 mr-3">
                        <User className="h-4 w-4 text-gray-600" />
                      </div>
                      <p className="text-sm font-medium">
                        {user?.primaryEmailAddress?.emailAddress}
                      </p>
                    </div>
                  </div>

                  <Separator />

                  {/* Organization Section */}
                  <div className="py-4 mb-4">
                    <h3 className="text-sm font-medium mb-3 px-6">Organization</h3>
                    <div className="flex items-center gap-3 px-6 py-2">
                      <div className="flex items-center justify-center h-9 w-9 rounded-md bg-gray-100">
                        <Building className="h-4 w-4 text-gray-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">
                          {organization?.organization?.name || "No Organization"}
                        </p>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* Navigation Section */}
                  <div className="py-4">
                    <h3 className="text-sm font-medium mb-3 px-6">Navigation</h3>

                    <nav className="space-y-1">
                      {/* Home Link */}
                      <SheetClose asChild>
                        <Link
                          to="/"
                          className="flex items-center h-10 px-6 hover:bg-gray-50 text-sm font-medium"
                        >
                          <div className="flex items-center justify-center h-9 w-9 mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 text-gray-600">
                              <title>Home Icon</title>
                              <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
                              <polyline points="9 22 9 12 15 12 15 22" />
                            </svg>
                          </div>
                          Home
                        </Link>
                      </SheetClose>

                      {/* Settlements Link */}
                      <SheetClose asChild>
                        <Link
                          to="/settlements"
                          className="flex items-center h-10 px-6 hover:bg-gray-50 text-sm font-medium"
                        >
                          <div className="flex items-center justify-center h-9 w-9 mr-3">
                            <FileBadge className="h-4 w-4 text-gray-600" />
                          </div>
                          Settlements
                        </Link>
                      </SheetClose>

                      {/* Title Company Form Link */}
                      <SheetClose asChild>
                        <Link
                          to={`/${organization?.organization?.slug || organization?.organization?.id}`}
                          className="flex items-center h-10 px-6 hover:bg-gray-50 text-sm font-medium"
                        >
                          <div className="flex items-center justify-center h-9 w-9 mr-3">
                            <Building2 className="h-4 w-4 text-gray-600" />
                          </div>
                          Title Company Form
                        </Link>
                      </SheetClose>
                    </nav>
                  </div>
                </div>

                {/* Sign Out Button */}
                <div className="absolute bottom-0 left-0 right-0 p-6 border-t">
                  <Button
                    variant="ghost"
                    className="w-full justify-start text-base font-medium text-gray-700"
                    onClick={() => signOut()}
                  >
                    <div className="flex items-center justify-center h-9 w-9 mr-3">
                      <LogOut className="h-5 w-5 text-gray-600" />
                    </div>
                    Sign Out
                  </Button>
                </div>
              </SheetContent>
            </Sheet>
          </SignedIn>

          {/* Sign In button removed */}
        </div>
      </div>
    </header>
  );
}
