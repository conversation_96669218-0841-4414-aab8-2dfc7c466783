import { useState } from "react";
import { Check, Copy } from "lucide-react";
import { cn } from "~/lib/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "~/components/ui/tooltip";

interface ResourceIdProps {
  id: string;
  label?: string;
  className?: string;
  monospace?: boolean;
  truncate?: boolean;
  maxLength?: number;
}

export function ResourceId({
  id,
  label,
  className,
  monospace = true,
  truncate = true,
  maxLength = 12,
}: ResourceIdProps) {
  const [copied, setCopied] = useState(false);

  const displayId =
    truncate && id.length > maxLength ? `${id.substring(0, maxLength)}...` : id;

  const copyToClipboard = () => {
    navigator.clipboard.writeText(id).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };

  return (
    <TooltipProvider>
      <div className={cn("inline-flex items-center gap-1 group", className)}>
        {label && (
          <span className="text-muted-foreground text-sm mr-1">{label}:</span>
        )}
        <Tooltip>
          <TooltipTrigger asChild>
            <button
              type="button"
              className={cn(
                "text-xs bg-muted px-1.5 py-0.5 rounded flex items-center gap-1",
                monospace ? "font-mono" : "",
                "cursor-pointer hover:bg-muted/80 transition-colors",
              )}
              onClick={copyToClipboard}
            >
              {displayId}
              <span className="opacity-0 group-hover:opacity-100 transition-opacity">
                {copied ? (
                  <Check className="h-3 w-3 text-green-500" />
                ) : (
                  <Copy className="h-3 w-3 text-muted-foreground" />
                )}
              </span>
            </button>
          </TooltipTrigger>
          <TooltipContent side="top">
            {copied ? "Copied!" : "Click to copy"}
          </TooltipContent>
        </Tooltip>
      </div>
    </TooltipProvider>
  );
}
