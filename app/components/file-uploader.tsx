import { useState, useRef, type KeyboardEvent } from 'react';
import { Loader2, Upload, CheckCircle } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';

interface FileUploaderProps {
  onUploadComplete?: (key: string) => void;
  className?: string;
  maxSizeMB?: number;
  acceptedFileTypes?: string;
  compact?: boolean;
}

export function FileUploader({
  onUploadComplete,
  className = '',
  maxSizeMB = 10,
  acceptedFileTypes = '.pdf',
  compact = false
}: FileUploaderProps) {
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadedKey, setUploadedKey] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const selectedFile = e.target.files[0];
      setFile(selectedFile);
      setError(null);
      setUploadedKey(null);

      // Auto upload the file
      await uploadFile(selectedFile);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const droppedFile = e.dataTransfer.files[0];
      setFile(droppedFile);
      setError(null);
      setUploadedKey(null);

      // Auto upload the file
      await uploadFile(droppedFile);
    }
  };

  // Extract the upload logic to a separate function that takes a file parameter
  const uploadFile = async (fileToUpload: File) => {
    if (!fileToUpload) {
      setError('No file to upload');
      return;
    }

    // Check file size
    const fileSizeInMB = fileToUpload.size / (1024 * 1024);
    if (fileSizeInMB > maxSizeMB) {
      setError(`File size exceeds ${maxSizeMB}MB limit`);
      return;
    }

    // Check file type
    const fileExtension = fileToUpload.name.split('.').pop()?.toLowerCase();
    const acceptedTypes = acceptedFileTypes.split(',').map(type => type.trim().replace('.', '').toLowerCase());

    if (fileExtension && !acceptedTypes.includes(fileExtension)) {
      setError(`Only ${acceptedFileTypes} files are allowed`);
      return;
    }

    setUploading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('file', fileToUpload);

      const response = await fetch('/upload', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json() as { success: boolean; key: string };

      if (!response.ok) {
        throw new Error((result as { error?: string }).error || 'Failed to upload file');
      }

      setUploadedKey(result.key);
      if (onUploadComplete) {
        onUploadComplete(result.key);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setUploading(false);
    }
  };

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  // Check if we're using the compact mode (no card styling)
  const isCompact = compact || className.includes('border-none') || className.includes('shadow-none');

  const uploadArea = (
    <>
      <div
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={!uploading ? handleButtonClick : undefined}
        onKeyDown={(e: KeyboardEvent) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            if (!uploading) handleButtonClick();
          }
        }}
        aria-label="Upload file"
        className={`border-2 border-dashed ${uploading ? 'border-primary bg-primary/5' : uploadedKey ? 'border-green-500 bg-green-50' : 'border-gray-300'} rounded-lg ${compact ? 'p-3' : 'p-6'} flex flex-col items-center justify-center ${!uploading ? 'cursor-pointer hover:border-primary' : ''} transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2`}
      >
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          className="hidden"
          accept={acceptedFileTypes}
        />
        {uploading ? (
          <>
            <Loader2 className={`${compact ? 'h-5 w-5' : 'h-10 w-10'} text-primary mb-1 animate-spin`} />
            <p className="text-xs text-primary">Uploading file...</p>
          </>
        ) : uploadedKey ? (
          <>
            <CheckCircle className={`${compact ? 'h-5 w-5' : 'h-10 w-10'} text-green-500 mb-1`} />
            <p className="text-xs text-green-700">{compact ? 'File uploaded' : 'File uploaded successfully!'}</p>
            {!compact && <p className="text-xs text-green-600 mt-1">{file?.name}</p>}
            <p className="text-xs text-green-500 mt-1">Click to replace</p>
          </>
        ) : (
          <>
            <Upload className={`${compact ? 'h-5 w-5' : 'h-10 w-10'} text-gray-400 mb-1`} />
            <p className="text-xs text-gray-500">
              {file ? file.name : 'Click to browse or drag and drop'}
            </p>
            {!file && (
              <p className="text-xs text-gray-400 mt-1">
                Max file size: {maxSizeMB}MB
              </p>
            )}
            {file && (
              <p className="text-xs text-gray-400 mt-1">
                {(file.size / (1024 * 1024)).toFixed(2)} MB
              </p>
            )}
          </>
        )}
      </div>

      {error && (
        <p className="text-sm text-red-500 mt-2">{error}</p>
      )}
    </>
  );

  // If we're in compact mode, just return the upload area
  if (isCompact) {
    return uploadArea;
  }

  // Otherwise, wrap it in a card
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>File Upload</CardTitle>
        <CardDescription>
          Drag and drop a file or click to select
        </CardDescription>
      </CardHeader>
      <CardContent>
        {uploadArea}
      </CardContent>
      {/* Remove the footer with the upload button since we're auto-uploading */}
    </Card>
  );
}
