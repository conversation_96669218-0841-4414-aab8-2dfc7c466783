import { cn } from "~/lib/utils";

export const Backdrop = (props: {
  variant?: "centered" | "full";
  children?: React.ReactNode;
}) => {
  return (
    <div
      className={cn(
        "min-h-screen bg-gradient-to-b from-blue-500 via-blue-400 to-green-400 flex flex-col p-4 md:p-8",
        props.variant === "centered" && "items-center justify-center",
        props.variant === "full" && "items-start justify-start",
      )}
    >
      {props.children}
    </div>
  );
};
