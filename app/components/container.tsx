import { Header } from "./header";
import { cn } from "~/lib/utils";

type ContainerProps = {
  children: React.ReactNode;
  variant?: "property-manager" | "title-company" | "home" | "neutral";
  width?: "default" | "narrow" | "wide" | "full";
  showHeader?: boolean;
  className?: string;
};

export const Container = ({
  children,
  variant = "neutral",
  width = "default",
  showHeader = true,
  className,
}: ContainerProps) => {
  // Define the gradient classes based on the variant
  let gradientClass = "";

  switch (variant) {
    case "property-manager":
      gradientClass = "bg-gradient-to-b from-blue-400 via-green-400 to-green-300";
      break;
    case "title-company":
      gradientClass = "bg-gradient-to-b from-blue-700 via-blue-400 to-blue-200";
      break;
    case "home":
      gradientClass = "bg-gradient-to-b from-blue-500 via-blue-400 to-green-400";
      break;
    case "neutral":
    default:
      gradientClass = "bg-gradient-to-b from-gray-50 to-gray-100";
  }

  // Define width classes
  let widthClass = "max-w-5xl"; // default width

  switch (width) {
    case "narrow":
      widthClass = "max-w-3xl";
      break;
    case "wide":
      widthClass = "max-w-7xl";
      break;
    case "full":
      widthClass = "max-w-none";
      break;
    case "default":
    default:
      widthClass = "max-w-5xl";
  }

  return (
    <div className={cn(`min-h-screen flex flex-col ${gradientClass}`, className)}>
      {showHeader && <Header />}
      <main className={`flex-1 container ${widthClass} mx-auto px-6 md:px-8 pt-4`}>
        <div className={cn("flex flex-col space-y-4", variant === "property-manager" ? "bg-white rounded-lg shadow-md p-8 border border-gray-100" : "")}>
          {children}
        </div>
      </main>
    </div>
  );
};
