import React, { useState, useEffect } from "react";
import { loadStripe } from "@stripe/stripe-js";
import { Elements } from "@stripe/react-stripe-js";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog";
import { ACHPaymentForm } from "./ach-payment-form";
import { useCreatePaymentIntent } from "~/api/payments/payments-hooks";
import { Loader2 } from "lucide-react";

// Initialize Stripe with your publishable key
// In a real app, this would come from environment variables
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || "");

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  settlementId: string;
  amount: number;
  onPaymentSuccess: () => void;
}

export function PaymentModal({
  isOpen,
  onClose,
  settlementId,
  amount,
  onPaymentSuccess,
}: PaymentModalProps) {
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const { mutate: createPaymentIntent, isPending } = useCreatePaymentIntent();

  useEffect(() => {
    if (isOpen && amount > 0) {
      // Create a payment intent when the modal opens
      createPaymentIntent(
        {
          settlement_id: settlementId,
          amount: amount,
          payment_method_type: "ach_debit",
          currency: "usd",
          metadata: {
            settlement_id: settlementId,
          },
        },
        {
          onSuccess: (data) => {
            setClientSecret(data.client_secret);
          },
          onError: (error) => {
            console.error("Error creating payment intent:", error);
          },
        }
      );
    }
  }, [isOpen, settlementId, amount, createPaymentIntent]);

  const handleClose = () => {
    setClientSecret(null);
    onClose();
  };

  const handleSuccess = () => {
    setClientSecret(null);
    onPaymentSuccess();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Make a Payment</DialogTitle>
          <DialogDescription>
            Complete your payment using ACH bank transfer
          </DialogDescription>
        </DialogHeader>

        {isPending ? (
          <div className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-sky-600 mb-4" />
            <p className="text-sm text-muted-foreground">Preparing payment form...</p>
          </div>
        ) : clientSecret ? (
          <Elements
            stripe={stripePromise}
            options={{
              clientSecret,
              appearance: {
                theme: "stripe",
                variables: {
                  colorPrimary: "#0284c7",
                  colorBackground: "#ffffff",
                  colorText: "#1e293b",
                  colorDanger: "#ef4444",
                  fontFamily: "ui-sans-serif, system-ui, sans-serif",
                  spacingUnit: "4px",
                  borderRadius: "8px",
                },
              },
            }}
          >
            <ACHPaymentForm
              amount={amount}
              onSuccess={handleSuccess}
              onCancel={handleClose}
            />
          </Elements>
        ) : (
          <div className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-sky-600 mb-4" />
            <p className="text-sm text-muted-foreground">Loading payment options...</p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
