import { HousePlus } from "lucide-react";
import { useNavigate } from "react-router";

export const Logo = () => {
  const navigate = useNavigate();
  return (
    <button
      type="button"
      className="cursor-pointer flex items-center space-x-3 hover:bg-sky-100/80 rounded-md px-1 py-1 transition-colors"
      onClick={() => {
        navigate("/");
      }}
    >
      <span className="w-9 h-9 rounded flex items-center justify-center text-white bg-gradient-to-b from-sky-700 to-sky-900">
        <img src="/titlesync-logo.png" alt="TitleSync Logo" className="rounded" />
      </span>
      <span className="font-bold text-xl text-gray-800">TitleSync</span>
    </button>
  );
};
