import { useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";

interface StatusFilterProps {
  value?: string;
  onChange?: (value: string) => void;
  statuses: string[];
  className?: string;
}

export function StatusFilter({
  value,
  onChange,
  statuses,
  className,
}: StatusFilterProps) {
  const [selectedStatus, setSelectedStatus] = useState(value || "");

  // Update internal state when external value changes
  useEffect(() => {
    if (value !== undefined) {
      setSelectedStatus(value);
    }
  }, [value]);

  const handleChange = (newValue: string) => {
    setSelectedStatus(newValue);
    onChange?.(newValue);
  };

  return (
    <Select value={selectedStatus} onValueChange={handleChange}>
      <SelectTrigger className={`w-[180px] border-gray-300 h-10 shadow-sm ${className}`}>
        <SelectValue placeholder="Filter by status" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">All statuses</SelectItem>
        {statuses.map((status) => (
          <SelectItem key={status} value={status}>
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
