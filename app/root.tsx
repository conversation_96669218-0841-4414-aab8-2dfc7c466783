import {
  <PERSON>s,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  ScrollRestoration,
  isRouteErrorResponse,
} from "react-router-dom";
import type { Route } from "./+types/root";
import "./app.css";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  OrganizationSwitcher,
  SignInButton,
  SignedIn,
  SignedOut,
  User<PERSON>utton,
  useClerk,
} from "@clerk/react-router";
import { rootAuthLoader } from "@clerk/react-router/ssr.server";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

// export const links: Route.LinksFunction = () => [
//   { rel: "preconnect", href: "https://fonts.googleapis.com" },
//   {
//     rel: "preconnect",
//     href: "https://fonts.gstatic.com",
//     crossOrigin: "anonymous",
//   },
//   {
//     rel: "stylesheet",
//     href: "https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap",
//   },
// ];

const queryClient = new QueryClient();

export async function loader(args: Route.LoaderArgs) {
  // Not sure why host is wrong here in development...
  if (args.request.headers.get("host")?.startsWith("127.0.0.1:")) {
    args.request.headers.set("host", "localhost:1234");
  }

  return rootAuthLoader(args, {
    publishableKey: args.context.cloudflare.env.VITE_CLERK_PUBLISHABLE_KEY,
    secretKey: args.context.cloudflare.env.CLERK_SECRET_KEY,
  });
}

export function Layout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body>
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}

export default function App({ loaderData }: Route.ComponentProps) {
  return (
    <ClerkProvider
      appearance={{
        variables: {
          colorPrimary: "#10b981", /* Green-600 for buttons */
          colorBackground: "#3b82f6", /* Blue-500 for background */
          colorText: "#1e293b",
          colorDanger: "#ef4444",
          colorSuccess: "#10b981",
          borderRadius: "0.75rem",
        },
        layout: {
          logoPlacement: "inside",
          showOptionalFields: false,
          socialButtonsVariant: "iconButton",
          logoImageUrl: "/titlesync-logo.png" // Use TitleSync logo
        },
        elements: {
          rootBox: {
            backgroundColor: "linear-gradient(to bottom, #3b82f6, #10b981)",
            minHeight: "100vh",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          },
          card: {
            boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
            borderRadius: "0.75rem",
            maxWidth: "400px",
            width: "100%",
            padding: "1.5rem",
            border: "1px solid rgba(229, 231, 235, 1)",
          },
          headerTitle: {
            fontSize: "1.5rem",
            fontWeight: "600",
            textAlign: "center",
            marginBottom: "0.5rem",
          },
          headerSubtitle: {
            fontSize: "0.875rem",
            color: "#6B7280",
            textAlign: "center",
            marginBottom: "1.5rem",
          },
          formButtonPrimary: {
            backgroundColor: "#10b981",
            borderRadius: "0.75rem",
            fontSize: "1rem",
            padding: "0.75rem 1rem",
            fontWeight: "500",
            width: "100%",
            boxShadow: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
            marginTop: "0.5rem",
          },
          formFieldLabel: {
            fontSize: "0.875rem",
            fontWeight: "500",
            color: "#374151",
            marginBottom: "0.5rem",
          },
          formFieldInput: {
            borderRadius: "0.5rem",
            padding: "0.75rem 1rem",
            fontSize: "1rem",
            borderColor: "#E5E7EB",
            boxShadow: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
          },
          footerActionLink: {
            color: "#3B82F6",
            fontWeight: "500",
          },
          footer: {
            marginTop: "1.5rem",
            textAlign: "center",
          },
          logoImage: {
            width: "auto",
            height: "40px",
            margin: "0 auto 1rem auto",
            display: "block",
          },
          socialButtonsIconButton: {
            border: "1px solid #E5E7EB",
            borderRadius: "0.5rem",
            boxShadow: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
          },
          dividerLine: {
            backgroundColor: "#E5E7EB",
          },
          dividerText: {
            color: "#6B7280",
            fontSize: "0.875rem",
          },
          identityPreviewEditButton: {
            color: "#3B82F6",
          },
          formFieldAction: {
            color: "#3B82F6",
          },
          alert: {
            borderRadius: "0.5rem",
            fontSize: "0.875rem",
          }
        }
      }}
      loaderData={loaderData}
      signUpFallbackRedirectUrl="/"
      signInFallbackRedirectUrl="/"
    >
      <Outlet />
    </ClerkProvider>
  );
}

export function ErrorBoundary({ error }: Route.ErrorBoundaryProps) {
  let message = "Oops!";
  let details = "An unexpected error occurred.";
  let stack: string | undefined;

  if (isRouteErrorResponse(error)) {
    message = error.status === 404 ? "404" : "Error";
    details =
      error.status === 404
        ? "The requested page could not be found."
        : error.statusText || details;
  } else if (import.meta.env.DEV && error && error instanceof Error) {
    details = error.message;
    stack = error.stack;
  }

  return (
    <main className="pt-16 p-4 container mx-auto border">
      <h1>{message}</h1>
      <p>{details}</p>
      {stack && (
        <pre className="w-full p-4 overflow-x-auto">
          <code>{stack}</code>
        </pre>
      )}
    </main>
  );
}
