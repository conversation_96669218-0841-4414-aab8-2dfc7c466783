import { Container } from "~/components/container";
import { useHomeownerAssociations } from "~/api/homeowner-associations/homeowner-associations-hooks";
import HomeownerAssociationsTable from "~/api/homeowner-associations/ui/homeowner-associations-table";
import { CreateHomeownerAssociationSheet } from "~/api/homeowner-associations/ui/create-homeowner-association-sheet";
import { DebugData } from "~/components/debug";

export default function HomeownerAssociationsListPage() {
  const {
    data: homeownerAssociationsData,
    isLoading,
    error,
  } = useHomeownerAssociations();

  return (
    <Container>
      <div className="flex justify-between items-center">
        <h1 className="text-xl font-bold">Homeowner Associations</h1>
        <CreateHomeownerAssociationSheet />
      </div>

      {isLoading ? (
        <div>Loading homeowner associations...</div>
      ) : error ? (
        <div className="text-red-500">Error loading homeowner associations</div>
      ) : (
        <>
          <HomeownerAssociationsTable
            homeownerAssociations={homeownerAssociationsData?.items ?? []}
          />
        </>
      )}
    </Container>
  );
}
