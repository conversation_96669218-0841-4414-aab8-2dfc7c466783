import {
  SignInButton,
  SignUp<PERSON>utton,
  SignedIn,
  SignedOut,
  useOrganization,
  useSignIn,
  useUser,
} from "@clerk/react-router";
import { Container } from "~/components/container";
import { JsonView } from "~/components/json";
import { Button } from "~/components/ui/button";

export default function AuthIndexPage() {
  const user = useUser();
  const organization = useOrganization();

  const signin = useSignIn();

  return (
    <Container>
      <h1>Authentication</h1>
      <SignedOut>
        <p>You are not authenticated</p>

        <SignInButton>
          <Button variant="outline">Sign In</Button>
        </SignInButton>
        <br />
        <SignUpButton>
          <Button variant="outline">Sign Up</Button>
        </SignUpButton>
      </SignedOut>
      <SignedIn>
        <p>You are authenticated</p>
        <hr />
        <JsonView
          data={{
            userId: user.user?.id,
            email: user.user?.primaryEmailAddress,
          }}
        />
        <hr />
        <JsonView
          data={{
            organizationId: organization.organization?.id,
            name: organization.organization?.name,
          }}
        />
      </SignedIn>
    </Container>
  );
}
