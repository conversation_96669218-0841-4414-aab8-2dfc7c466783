// This file re-exports the main title-company component
// It's used to support the direct slug-based URL pattern
import TitleCompanyComponent from "./title-company";
import { useParams, useNavigate } from "react-router";
import { useEffect } from "react";

export default function DirectOrgSlugPage() {
    // Extract the org_slug from the URL params
    const params = useParams<{ org_slug: string }>();
    const navigate = useNavigate();

    console.log("DirectOrgSlugPage - org_slug:", params.org_slug);

    // If no org_slug is provided, redirect to the title-companies page
    useEffect(() => {
        if (!params.org_slug) {
            console.log("No org_slug provided, redirecting to title-companies page");
            navigate("/title-companies");
        }
    }, [params.org_slug, navigate]);

    // Pass the org_slug to the main component
    return <TitleCompanyComponent />;
}
