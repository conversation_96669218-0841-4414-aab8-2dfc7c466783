import * as React from "react";
import {
  SignedIn,
  SignedOut,
  useAuth,
  useOrganization,
  useUser,
} from "@clerk/react-router";
import { Building2, Check, Copy, FileText, Info, LogOut, Mail, Plus, User, Users } from "lucide-react";

import { <PERSON><PERSON> } from "~/components/ui/button";
import { Container } from "~/components/container";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "~/components/ui/sheet";
import { Label } from "~/components/ui/label";
import { Separator } from "~/components/ui/separator";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "~/components/ui/tooltip";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "~/components/ui/tabs";
import { Input } from "~/components/ui/input";
import type { Route } from "./+types/home";
import { Logo } from "~/components/logo";
import { Link } from "react-router";

export function meta() {
  return [
    { title: "TitleSync" },
    { name: "description", content: "TitleSync" },
  ];
}

export async function loader(args: Route.LoaderArgs) {
  return {
    message: args.context.cloudflare.env.VALUE_FROM_CLOUDFLARE,
  };
}

export default function LandingPage(_props: Route.ComponentProps) {
  const { user } = useUser();
  const { organization } = useOrganization();
  const { signOut } = useAuth();
  const [showInitiateSheet, setShowInitiateSheet] = React.useState(false);
  const [copySuccess, setCopySuccess] = React.useState(false);
  const [emailValue, setEmailValue] = React.useState("");
  const [emailSent, setEmailSent] = React.useState(false);
  const [isEmailValid, setIsEmailValid] = React.useState(true);

  const handleCopy = async () => {
    const url = `https://www.title-sync.com/${organization?.slug || "clayton-property-demo"}`;
    try {
      await navigator.clipboard.writeText(url);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmailValue(e.target.value);
    // Simple email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    setIsEmailValid(emailRegex.test(e.target.value) || e.target.value === "");
  };

  const handleSendEmail = () => {
    if (emailValue && isEmailValid) {
      // Here you would implement the actual email sending logic
      console.log(`Sending invitation to: ${emailValue}`);
      setEmailSent(true);
      setTimeout(() => {
        setEmailSent(false);
        setEmailValue("");
      }, 2000);
    }
  };

  return (
    <>
      <SignedIn>
        <Container variant="home" showHeader={true}>
          <div className="flex flex-col items-center justify-center py-8 px-4 sm:px-6">
            <Card className="w-full max-w-sm sm:max-w-md shadow-lg bg-white rounded-2xl border border-gray-200">
              <CardHeader className="flex flex-col items-center space-y-3 pb-4 px-6 sm:px-8 pt-6 sm:pt-8 border-b border-gray-100">
                <CardTitle className="text-2xl md:text-3xl text-center font-semibold">
                  Welcome to TitleSync
                </CardTitle>
                <h2 className="text-xl text-center font-medium text-blue-700">
                  {organization?.name || "---"}
                </h2>
                <CardDescription className="text-center text-gray-500 text-sm">
                  Simplify HOA settlements
                </CardDescription>
              </CardHeader>
              <CardContent className="px-6 sm:px-8 pb-6 pt-5">
                <div className="flex items-center gap-3 mb-5 p-2 bg-gray-50 rounded-lg shadow-sm">
                  <div className="flex items-center justify-center h-7 w-7 bg-white rounded-full shadow-sm">
                    <User className="h-4 w-4 text-gray-600" />
                  </div>
                  <span className="text-sm text-gray-700">
                    {user?.primaryEmailAddress?.emailAddress}
                  </span>
                </div>

                <div className="grid grid-cols-1 gap-4">
                  <Button
                    variant="default"
                    size="lg"
                    className="w-full py-4 bg-green-600 hover:bg-green-700 transition-all flex items-center justify-center gap-3 text-base shadow-sm rounded-lg"
                    asChild
                  >
                    <Link to="/settlements">
                      <FileText className="h-5 w-5" />
                      <span>View Settlements</span>
                    </Link>
                  </Button>

                  <Sheet>
                    <SheetTrigger asChild>
                      <Button
                        className="w-full py-4 bg-sky-600 hover:bg-sky-700 transition-all flex items-center justify-center gap-3 text-base shadow-sm rounded-lg"
                      >
                        <Plus className="h-5 w-5" />
                        <span>Initiate Settlement</span>
                      </Button>
                    </SheetTrigger>
                    <SheetContent className="sm:max-w-md">
                      <SheetHeader className="pb-4">
                        <SheetTitle className="text-xl">Initiate Settlement</SheetTitle>
                        <SheetDescription className="text-base">
                          Invite a title company to submit settlement details
                        </SheetDescription>
                      </SheetHeader>
                      <div className="px-6">
                        <Tabs defaultValue="share" className="py-6">
                          <TabsList className="grid w-full grid-cols-2 mb-6">
                            <TabsTrigger value="share">Share Link</TabsTrigger>
                            <TabsTrigger value="email">Send Email</TabsTrigger>
                          </TabsList>

                          <TabsContent value="share" className="space-y-4">
                            <div className="space-y-4">
                              <div className="flex items-center">
                                <h3 className="text-base font-medium text-gray-700">Share Settlement Link</h3>
                              </div>
                              <div className="grid gap-4">
                                <Label htmlFor="settlement-url" className="text-sm font-medium text-gray-700">Settlement URL</Label>
                                <div className="flex gap-2">
                                  <Input
                                    id="settlement-url"
                                    readOnly
                                    value={`https://www.title-sync.com/${organization?.slug || "clayton-property-demo"}`}
                                    className="font-mono text-sm shadow-sm border rounded-lg"
                                  />
                                  <Button
                                    onClick={handleCopy}
                                    className="bg-gray-600 hover:bg-gray-700 shadow-sm rounded-lg"
                                  >
                                    {copySuccess ? (
                                      <>
                                        <Check className="h-4 w-4 mr-2" />
                                        Copied
                                      </>
                                    ) : (
                                      <>
                                        <Copy className="h-4 w-4 mr-2" />
                                        Copy
                                      </>
                                    )}
                                  </Button>
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  <p>
                                    Copy this unique URL and share it with the title company via email to allow them to submit settlement details directly to you.
                                  </p>
                                </div>
                              </div>
                            </div>
                          </TabsContent>

                          <TabsContent value="email" className="space-y-4">
                            <div className="space-y-4">
                              <div className="flex items-center">
                                <h3 className="text-base font-medium text-gray-700">Send Email Invitation</h3>
                              </div>
                              <div className="grid gap-4">
                                <Label htmlFor="title-company-email" className="text-sm font-medium text-gray-700">Title Company Email</Label>
                                <div className="flex gap-2">
                                  <Input
                                    id="title-company-email"
                                    placeholder="<EMAIL>"
                                    value={emailValue}
                                    onChange={handleEmailChange}
                                    type="email"
                                    className="shadow-sm border rounded-lg"
                                  />
                                  <Button
                                    onClick={handleSendEmail}
                                    disabled={!emailValue || !isEmailValid}
                                    className="bg-gray-600 hover:bg-gray-700 shadow-sm rounded-lg"
                                  >
                                    <Mail className="h-4 w-4 mr-2" />
                                    {emailSent ? "Sent!" : "Send"}
                                  </Button>
                                </div>
                                {!isEmailValid && emailValue && (
                                  <p className="text-xs text-red-500 mt-1">Please enter a valid email address</p>
                                )}
                                <div className="text-sm text-muted-foreground">
                                  <p>
                                    Send an email invitation to the title company with a link to submit settlement details.
                                  </p>
                                </div>
                              </div>
                            </div>
                          </TabsContent>
                        </Tabs>

                        <SheetFooter className="pt-4 pb-2 border-t border-gray-100 mt-4">
                          <SheetClose asChild>
                            <Button variant="outline" className="w-full sm:w-auto shadow-sm rounded-lg">Close</Button>
                          </SheetClose>
                        </SheetFooter>
                      </div>
                    </SheetContent>
                  </Sheet>

                  <div className="pt-3 mt-1 border-t border-gray-200">
                    <Button
                      variant="outline"
                      className="w-full py-4 mt-3 flex items-center justify-center gap-3 text-base border hover:bg-gray-50 text-gray-700 rounded-lg shadow-sm"
                      onClick={() => signOut()}
                    >
                      <LogOut className="h-5 w-5" />
                      <span>Sign Out</span>
                    </Button>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-center pb-6 pt-2 border-t border-gray-100 mt-4">
                <p className="text-xs text-muted-foreground opacity-70">
                  © 2025 TitleSync
                </p>
              </CardFooter>
            </Card>
          </div>
        </Container>
      </SignedIn>

      <SignedOut>
        <Container variant="home" showHeader={true}>
          <div className="flex flex-col items-center justify-center py-8 px-4 sm:px-6">
            <Card className="w-full max-w-sm sm:max-w-md shadow-lg bg-white rounded-2xl border border-gray-200">
              <CardHeader className="flex flex-col items-center space-y-3 pb-4 px-6 sm:px-8 pt-6 sm:pt-8 border-b border-gray-100">
                <CardTitle className="text-2xl md:text-3xl text-center font-semibold">
                  Welcome to TitleSync
                </CardTitle>
                <CardDescription className="text-center text-gray-500 text-sm">
                  Simplify HOA settlements
                </CardDescription>
              </CardHeader>
              <CardContent className="px-6 sm:px-8 pb-6 pt-5">
                <div className="text-center mb-4">
                  <p className="text-base font-medium text-gray-700">
                    Select your role
                  </p>
                </div>

                <div className="grid grid-cols-1 gap-4">
                  <Button
                    variant="default"
                    size="lg"
                    className="w-full py-4 bg-blue-600 hover:bg-blue-700 transition-all flex items-center justify-center gap-3 text-base shadow-sm rounded-lg"
                    asChild
                  >
                    <Link to="/title-companies">
                      <Building2 className="h-5 w-5" />
                      <span>Title Company</span>
                    </Link>
                  </Button>

                  <Button
                    variant="default"
                    size="lg"
                    className="w-full py-4 bg-green-600 hover:bg-green-700 transition-all flex items-center justify-center gap-3 text-base shadow-sm rounded-lg"
                    asChild
                  >
                    <Link to="/settlements">
                      <Users className="h-5 w-5" />
                      <span>HOA / Property Manager</span>
                    </Link>
                  </Button>
                </div>
              </CardContent>
              <CardFooter className="flex justify-center pb-6 pt-2 border-t border-gray-100 mt-4">
                <p className="text-xs text-muted-foreground opacity-70">
                  © 2025 TitleSync
                </p>
              </CardFooter>
            </Card>
          </div>
        </Container>
      </SignedOut>
    </>
  );
}
