import { Outlet } from "react-router";
import { Link } from "react-router";
import { Logo } from "~/components/logo";
import { Backdrop } from "~/components/backdrop";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import {
  OrganizationSwitcher,
  SignedIn,
  SignedOut,
  SignInButton,
  useOrganization,
  UserButton,
  useUser,
} from "@clerk/react-router";
import { Button } from "~/components/ui/button";
import { Loading } from "~/components/loading";
import { Container } from "~/components/container";

export default function Layout() {
  const user = useUser();
  const organization = useOrganization();

  if (!user.isLoaded) {
    return <Loading />;
  }

  return (
    <>
      <SignedOut>
        <Backdrop variant="centered">
          <Card className="w-full max-w-md shadow-lg">
            <CardHeader className="flex flex-col items-center space-y-2 pb-1 pt-8 border-b-0">
              <Logo />
              <CardTitle className="text-xl md:text-2xl text-center">
                Property Manager Portal
              </CardTitle>
              <CardDescription className="text-center">
                Manage your settlements and property documents
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-6 pb-2 space-y-8">
              <SignInButton>
                <Button
                  size="lg"
                  className="w-full py-6 bg-green-600 hover:bg-green-700 transition-all text-base font-medium rounded-xl">
                  Sign In to Continue
                </Button>
              </SignInButton>
            </CardContent>
          </Card>
        </Backdrop>
      </SignedOut>
      <SignedIn>
        {organization?.organization?.id ? (
          <Container key={organization?.organization?.id} variant="property-manager" width="wide">
            <Outlet />
          </Container>
        ) : (
          <Backdrop variant="centered">
            <Card className="w-full max-w-md shadow-lg">
              <CardHeader className="flex flex-col items-center space-y-4 pb-2">
                <Logo />
                <CardTitle className="text-xl md:text-2xl text-center">
                  No Organization Found
                </CardTitle>
                <CardDescription className="text-center">
                  You need to be part of an organization to access the dashboard
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="bg-amber-50 p-4 rounded-lg border border-amber-200 text-center">
                  <p className="text-amber-800 mb-4">
                    You are signed in, but you're not a member of any
                    organization. Please contact your administrator to get added
                    to an organization.
                  </p>

                  <div className="flex justify-center mt-4">
                    <OrganizationSwitcher
                      hidePersonal={false}
                      createOrganizationMode="modal"
                      afterCreateOrganizationUrl="/"
                      afterLeaveOrganizationUrl="/"
                      afterSelectOrganizationUrl="/"
                    />
                  </div>
                </div>

                <div className="flex items-center justify-between border-t border-gray-200 pt-4">
                  <div className="flex items-center gap-2">
                    <UserButton />
                    <div className="text-sm">
                      <p className="font-medium">
                        {user.user?.fullName || user.user?.username}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {user.user?.primaryEmailAddress?.emailAddress}
                      </p>
                    </div>
                  </div>

                  <Button variant="outline" size="sm" asChild>
                    <Link to="/">Return to Home</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </Backdrop>
        )}
      </SignedIn>
    </>
  );
}

// Removed sidebar components
