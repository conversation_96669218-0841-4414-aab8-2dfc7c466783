import { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router";
import { SuccessMessage } from "~/components/success-message";

// Define the success data type
interface SuccessData {
  title: string;
  message: string;
  resourceId?: string;
  resourceLabel?: string;
  propertyAddress?: string;
  organizationName: string;
}

export default function TitleCompanySuccessPage() {
  const navigate = useNavigate();
  const location = useLocation();
  const [successData, setSuccessData] = useState<SuccessData | null>(null);

  // Load the success data from all possible sources
  useEffect(() => {
    console.log("Success page mounted, attempting to load success data");

    // Try to get data from location state first (most reliable)
    if (location.state && location.state.successData) {
      console.log("Found success data in location state");
      setSuccessData(location.state.successData);
      return;
    }

    // Try sessionStorage next
    try {
      const sessionData = sessionStorage.getItem('settlementSuccessData');
      if (sessionData) {
        console.log("Found success data in sessionStorage");
        try {
          const parsedData = JSON.parse(sessionData);
          setSuccessData(parsedData);
          // Clear the data after loading it
          sessionStorage.removeItem('settlementSuccessData');
          return;
        } catch (parseError) {
          console.error('Error parsing sessionStorage data:', parseError);
        }
      }
    } catch (sessionError) {
      console.error("Error accessing sessionStorage:", sessionError);
    }

    // Try localStorage as a fallback
    try {
      const localData = localStorage.getItem('settlementSuccessData');
      if (localData) {
        console.log("Found success data in localStorage");
        try {
          const parsedData = JSON.parse(localData);
          setSuccessData(parsedData);
          // Clear the data after loading it
          localStorage.removeItem('settlementSuccessData');
          return;
        } catch (parseError) {
          console.error('Error parsing localStorage data:', parseError);
        }
      }
    } catch (localError) {
      console.error("Error accessing localStorage:", localError);
    }

    // If we get here, we couldn't find the data anywhere
    console.log("No success data found in any storage location");
    setTimeout(() => {
      console.log("Redirecting to home page due to missing success data");
      navigate('/');
    }, 1000);
  }, [navigate, location.state]);

  // Show loading state if data is not yet loaded
  if (!successData) {
    return (
      <div className="min-h-screen flex flex-col bg-gradient-to-b from-blue-700 via-blue-400 to-blue-200">
        {/* <TitleCompanyHeader /> */}
        <main className="flex-1 container mx-auto px-4 md:px-6 pt-8 flex items-center justify-center">
          <div className="bg-white p-6 rounded-lg shadow-md text-center">
            <div className="animate-pulse mb-4">
              <div className="h-12 w-12 bg-blue-200 rounded-full mx-auto" />
            </div>
            <h2 className="text-xl font-semibold mb-2">Loading Success Details</h2>
            <p className="text-gray-600">Please wait while we prepare your success information...</p>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-blue-700 via-blue-400 to-blue-200">
      {/* <TitleCompanyHeader /> */}
      <main className="flex-1 container mx-auto px-4 md:px-6 pt-8 flex items-center justify-center">
        <SuccessMessage
          title={successData.title}
          message={successData.message}
          resourceId={successData.resourceId}
          resourceLabel={successData.resourceLabel}
          returnUrl="/"
          returnLabel="Return to Home"
          propertyAddress={successData.propertyAddress}
        />
      </main>
    </div>
  );
}
