import { Info, Trash2, Building2, <PERSON><PERSON>2 } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Textarea } from "~/components/ui/textarea";
import { <PERSON><PERSON> } from "~/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "~/components/ui/tooltip";
import { useForm, useFieldArray } from "react-hook-form";
import { effectTsResolver } from "@hookform/resolvers/effect-ts";
import * as Schema from "effect/Schema";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";
import {
  EXPENSE_CATEGORIES,
  MONTHS,
  YEARS
} from "~/api/settlements/expense-categories";
import { usePara<PERSON>, useNavigate } from "react-router";
import { useState, useEffect } from "react";
import { Alert, AlertTitle, AlertDescription } from "~/components/ui/alert";
import {
  usePublicOrganization,
  usePublicOrganizationBySlug,
  useSubmitPublicSettlement,
} from "~/api/public/public-hooks";

import { FileUploader } from "~/components/file-uploader";
import { FileViewer } from "~/components/file-viewer";

// Define the form schema using Effect.ts
export const TitleCompanyFormSchema = Schema.Struct({
  propertyAddress: Schema.String,
  unit: Schema.optional(Schema.String),
  hoaName: Schema.optional(Schema.String),
  buyerEmail: Schema.optional(
    Schema.String.pipe(Schema.pattern(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)),
  ),
  warrantyDeed: Schema.optional(Schema.String), // Will store the file key
  sellerExpenses: Schema.Array(
    Schema.Struct({
      category: Schema.String,
      amount: Schema.Number.pipe(Schema.greaterThanOrEqualTo(0)),
      // Optional subcategory fields
      subcategory: Schema.optional(Schema.String), // For 'other' category
      month: Schema.optional(Schema.String),       // For 'dues' category
      year: Schema.optional(Schema.String),        // For 'dues' category
    }),
  ),
  buyerExpenses: Schema.Array(
    Schema.Struct({
      category: Schema.String,
      amount: Schema.Number.pipe(Schema.greaterThanOrEqualTo(0)),
      // Optional subcategory fields
      subcategory: Schema.optional(Schema.String), // For 'other' category
      month: Schema.optional(Schema.String),       // For 'dues' category
      year: Schema.optional(Schema.String),        // For 'dues' category
    }),
  ),
  notes: Schema.optional(Schema.String.pipe(Schema.maxLength(250))), // Special notes for the entire settlement
});

export type TitleCompanyFormValues = Schema.Schema.Type<
  typeof TitleCompanyFormSchema
>;

// Define the organization type
type Organization = {
  id: string;
  name: string;
  loading: boolean;
  error: string | null;
};

export default function TitleCompanyPage() {
  // Support both org_id and org_slug in the URL
  const params = useParams<{ org_id?: string; org_slug?: string }>();
  const org_id = params.org_id;
  const org_slug = params.org_slug;
  const navigate = useNavigate();

  console.log("TitleCompanyPage - params:", params);
  console.log("TitleCompanyPage - org_id:", org_id);
  console.log("TitleCompanyPage - org_slug:", org_slug);

  const [submitResult, setSubmitResult] = useState<{
    success: boolean;
    message: string;
    settlementId?: string;
  } | null>(null);

  // Show a loading state while the page is initializing
  const [pageReady, setPageReady] = useState(false);

  // Use the appropriate hook based on whether we have an ID or slug
  const {
    data: orgDataById,
    isLoading: orgLoadingById,
    error: orgErrorById,
  } = usePublicOrganization(org_id || "");

  const {
    data: orgDataBySlug,
    isLoading: orgLoadingBySlug,
    error: orgErrorBySlug,
  } = usePublicOrganizationBySlug(org_slug || "");

  // Log API call details for debugging
  console.log("API call details:", {
    org_id_param: org_id,
    org_slug_param: org_slug,
    orgDataById_loading: orgLoadingById,
    orgDataById_error: orgErrorById,
    orgDataById: orgDataById,
    orgDataBySlug_loading: orgLoadingBySlug,
    orgDataBySlug_error: orgErrorBySlug,
    orgDataBySlug: orgDataBySlug
  });

  // Combine the data from both hooks
  const orgData = orgDataById || orgDataBySlug;
  const orgLoading = orgLoadingById || orgLoadingBySlug;
  const orgError = orgErrorById || orgErrorBySlug;

  // Make sure we have a valid organization ID
  // If we have org data, use its ID; otherwise, try to use the org_id from the URL
  const actualOrgId = orgData?.id || "";

  // Log combined data
  console.log("Combined organization data:", {
    orgData,
    orgLoading,
    orgError,
    actualOrgId
  });

  // Use the new hook for submitting settlements
  const { mutate: submitSettlement, isPending: submitting } =
    useSubmitPublicSettlement();

  // Prepare organization data for the UI
  const organization: Organization = {
    id: actualOrgId,
    name: orgData?.name || "",
    loading: orgLoading,
    error: (!org_id && !org_slug)
      ? "No organization identifier provided. Please check the URL and try again."
      : orgError
        ? `Error loading organization: ${String(orgError)}. Please check the organization name and try again.`
        : (!orgLoading && !orgData)
          ? "Organization not found. Please check the organization name and try again."
          : null,
  };

  // Log organization data for debugging
  console.log("Organization data:", {
    id: actualOrgId,
    name: orgData?.name,
    loading: orgLoading,
    error: organization.error,
    orgDataById,
    orgDataBySlug
  });

  // Initialize form with react-hook-form
  const form = useForm<TitleCompanyFormValues>({
    resolver: effectTsResolver(TitleCompanyFormSchema),
    defaultValues: {
      propertyAddress: "",
      unit: "",
      hoaName: "",
      buyerEmail: "",
      warrantyDeed: "",
      sellerExpenses: [{ category: EXPENSE_CATEGORIES.OTHER, amount: 0, subcategory: "", month: "", year: "" }],
      buyerExpenses: [{ category: EXPENSE_CATEGORIES.OTHER, amount: 0, subcategory: "", month: "", year: "" }],
      notes: "",
    },
  });

  // Set up field arrays for dynamic expenses
  const {
    fields: sellerFields,
    append: appendSeller,
    remove: removeSeller,
  } = useFieldArray({
    control: form.control,
    name: "sellerExpenses",
  });

  const {
    fields: buyerFields,
    append: appendBuyer,
    remove: removeBuyer,
  } = useFieldArray({
    control: form.control,
    name: "buyerExpenses",
  });

  // Calculate totals
  const sellerTotal = form
    .watch("sellerExpenses")
    .reduce((sum, expense) => sum + (expense.amount || 0), 0);

  const buyerTotal = form
    .watch("buyerExpenses")
    .reduce((sum, expense) => sum + (expense.amount || 0), 0);

  const totalSettlement = sellerTotal + buyerTotal;

  // Use an effect to delay showing the form until after initial loading
  useEffect(() => {
    // If we're not loading anymore, or if we've been waiting for more than 1 second, show the form
    const timer = setTimeout(() => {
      setPageReady(true);
    }, 1000);

    if (!orgLoading) {
      setPageReady(true);
      clearTimeout(timer);
    }

    return () => clearTimeout(timer);
  }, [orgLoading]);

  const onSubmit = form.handleSubmit((data) => {
    // Enhanced validation for organization ID
    if (!orgData || !orgData.id) {
      console.error("Missing organization data or ID:", { orgData, actualOrgId });
      setSubmitResult({
        success: false,
        message: "Organization data is not available. Please refresh the page and try again.",
      });
      return;
    }

    // Ensure we have the organization ID from the API response
    const organizationId = orgData.id;
    console.log("Using organization ID for submission:", organizationId);

    if (!organizationId) {
      setSubmitResult({
        success: false,
        message: "No valid organization ID available. Please refresh the page and try again.",
      });
      return;
    }

    // Prepare the data for submission
    const submissionData = {
      org_id: organizationId,
      propertyAddress: data.propertyAddress,
      unit: data.unit,
      hoaName: data.hoaName,
      buyerEmail: data.buyerEmail,
      warranty_deed_key: data.warrantyDeed, // Include the warranty deed key
      sellerExpenses: data.sellerExpenses,
      buyerExpenses: data.buyerExpenses,
      totalAmount: totalSettlement,
      notes: data.notes,
    };

    // Log the submission data for debugging
    console.log("Submitting settlement data:", JSON.stringify(submissionData, null, 2));

    // Use the mutation hook to submit the data
    submitSettlement(submissionData, {
      onSuccess: (result) => {
        console.log("Settlement submission successful:", result);

        // Reset the form on success
        if (result.success) {
          form.reset();

          // Set a small delay before updating the state to ensure the redirect effect has time to run
          setTimeout(() => {
            console.log("Setting submitResult state");
            setSubmitResult({
              success: result.success,
              message: result.message,
              settlementId: result.settlementId,
            });
          }, 50);
        } else {
          // If not successful, update immediately
          setSubmitResult({
            success: result.success,
            message: result.message || "Failed to submit settlement. Please try again.",
            settlementId: result.settlementId,
          });
        }
      },
      onError: (error) => {
        console.error("Error submitting settlement:", error);

        // Provide more detailed error information
        let errorMessage = "An error occurred while submitting the settlement.";

        if (error instanceof Error) {
          errorMessage = `Error: ${error.message}`;
          // Add stack trace to console for debugging
          console.error("Error stack:", error.stack);
        } else if (typeof error === 'object' && error !== null) {
          errorMessage = `Error: ${JSON.stringify(error)}`;
        } else {
          errorMessage = `Error: ${String(error)}`;
        }

        setSubmitResult({
          success: false,
          message: errorMessage + " Please try again or contact support if the issue persists.",
        });
      },
    });
  });

  // Redirect to success page if submission was successful
  useEffect(() => {
    if (submitResult?.success) {
      console.log("Submission successful, preparing to redirect to success page");

      // Create the success data
      const successData = {
        title: "Settlement Submitted Successfully!",
        message: `Your settlement details for ${organization.name} have been submitted successfully.`,
        resourceId: submitResult.settlementId,
        resourceLabel: "Settlement ID",
        propertyAddress: form.getValues("propertyAddress"),
        organizationName: organization.name
      };

      console.log("Success data:", successData);

      try {
        // Try to store the success data in sessionStorage
        try {
          sessionStorage.setItem('settlementSuccessData', JSON.stringify(successData));
          console.log("Data stored in sessionStorage");
        } catch (storageError) {
          // Fallback to localStorage if sessionStorage fails
          console.warn("SessionStorage failed, falling back to localStorage:", storageError);
          localStorage.setItem('settlementSuccessData', JSON.stringify(successData));
          console.log("Data stored in localStorage");
        }

        // Use a small timeout to ensure the data is stored before navigation
        setTimeout(() => {
          console.log("Navigating to success page");
          // Add the data as URL state to ensure it's available
          navigate('/title-company-success', {
            state: { successData }
          });
        }, 100);
      } catch (error) {
        console.error("Error storing success data:", error);
        // Try direct navigation with state as a last resort
        navigate('/title-company-success', {
          state: { successData }
        });
      }
    }
  }, [submitResult?.success, organization.name, navigate]);

  return (
    <Form {...form}>
      <form
        onSubmit={onSubmit}
        className="flex flex-col space-y-4 w-full"
      >
        <div className="flex flex-col mb-2">
          <h1 className="text-3xl font-bold mb-2 text-center">
            Settlement Details Form
          </h1>
          <p className="text-gray-500 text-center text-sm mb-1">
            Submit settlement details for property transactions
          </p>
          <p className="text-red-500 text-center text-sm">
            * Required fields
          </p>
        </div>

        {/* Show loading indicator while page is initializing */}
        {!pageReady && (
          <div className="flex items-center justify-center p-6 border border-gray-200 rounded-lg">
            <Loader2 className="w-6 h-6 mr-3 animate-spin text-blue-500" />
            <span className="text-lg">Loading settlement form...</span>
          </div>
        )}

        {/* Organization Banner - only show when page is ready */}
        {pageReady && (
          organization.loading ? (
            <div className="flex items-center justify-center p-4 bg-gray-100 rounded-lg">
              <Loader2 className="w-5 h-5 mr-2 animate-spin" />
              <span>Loading organization details...</span>
            </div>
          ) : organization.error ? (
            <Alert variant="destructive" className="mb-6">
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{organization.error}</AlertDescription>

              <div>
                <br />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate("/title-companies")}
                >
                  Go Back to Title Companies
                </Button>
              </div>

            </Alert>
          ) : (
            <div className="flex items-center justify-center p-4 border border-gray-200 rounded-lg text-center">
              <Building2 className="w-5 h-5 mr-3 text-blue-600" />
              <span className="font-medium text-gray-800 text-lg">
                {organization.name}
              </span>
            </div>
          )
        )}

        {/* Submission Result */}
        {submitResult && (
          <Alert
            variant={submitResult.success ? "default" : "destructive"}
            className="mb-6"
          >
            <AlertTitle>
              {submitResult.success ? "Success" : "Error"}
            </AlertTitle>
            <AlertDescription>{submitResult.message}</AlertDescription>
            {submitResult.settlementId && (
              <p className="mt-2 text-sm">
                Settlement ID: {submitResult.settlementId}
              </p>
            )}
          </Alert>
        )}

        {/* Only show the form when the page is ready and there's no error */}
        {pageReady && !organization.error && (
          <>
            {/* Property Details Card */}
            <Card className="border border-gray-200">
              <CardHeader className="pb-3 border-b border-gray-100">
                <CardTitle className="text-xl text-gray-800">Property Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 pt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="propertyAddress"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Property Address <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="123 Main Street, City, State 12345"
                            className="py-5 px-4 text-base shadow-sm"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="unit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Unit (optional)</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Apt 4B or Unit 201"
                            className="py-5 px-4 text-base shadow-sm"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="hoaName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Homeowner Association Name (optional)</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Oakridge Community HOA"
                          className="py-5 px-4 text-base shadow-sm"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="buyerEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Buyer Email (optional)</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          className="py-5 px-4 text-base shadow-sm"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="warrantyDeed"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        <span>Warranty Deed (PDF only) <span className="text-red-500">*</span></span>
                      </FormLabel>
                      <FormControl>
                        <div>
                          {field.value ? (
                            <div className="flex items-center justify-between border rounded-md p-2">
                              <FileViewer
                                fileKey={field.value}
                                label="Warranty Deed"
                                maxFileNameLength={25}
                                className="flex-grow border-0 p-0 m-0"
                              />
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => field.onChange("")}
                                className="ml-2 shrink-0"
                              >
                                Replace
                              </Button>
                            </div>
                          ) : (
                            <FileUploader
                              onUploadComplete={(key) => field.onChange(key)}
                            />
                          )}
                          <input type="hidden" {...field} />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Seller Expenses Card */}
            <Card className="border border-gray-200">
              <CardHeader className="pb-3 border-b border-gray-100">
                <div className="flex items-center">
                  <CardTitle className="text-xl text-gray-800">Seller Expenses</CardTitle>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="ml-2 h-6 w-6"
                        >
                          <Info className="h-4 w-4 text-muted-foreground" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent className="p-3 max-w-sm">
                        <p className="text-sm">
                          Add expenses that will be paid by the seller at settlement.
                          If no expenses are paid, select "Other" category, enter $0.00 as the amount,
                          and add a description like "No fees paid". Use the Notes field for additional details (up to 250 characters).
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </CardHeader>
              <CardContent>
                {sellerFields.map((field, index) => (
                  <div
                    key={field.id}
                    className="border rounded-md p-4 mb-4 bg-gray-50/50 relative"
                  >
                    {/* Row number indicator */}
                    <div className="absolute -left-2 -top-2 bg-blue-600 text-white w-6 h-6 rounded-full flex items-center justify-center text-sm font-medium shadow-sm">
                      {index + 1}
                    </div>

                    {/* Main content */}
                    <div className="grid grid-cols-1 md:grid-cols-12 gap-4">
                      {/* Category section - 5 columns */}
                      <div className="md:col-span-5">
                        <FormField
                          control={form.control}
                          name={`sellerExpenses.${index}.category`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="flex items-center">
                                <span>Category</span> <span className="text-red-500 ml-1">*</span>
                              </FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger className="bg-white">
                                    <SelectValue placeholder="Select category" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value={EXPENSE_CATEGORIES.DUES}>
                                    Dues
                                  </SelectItem>
                                  <SelectItem value={EXPENSE_CATEGORIES.SPECIAL_ASSESSMENT}>
                                    Special Assessment
                                  </SelectItem>
                                  <SelectItem value={EXPENSE_CATEGORIES.MOVE_IN_OUT_FEE}>
                                    Move In/Out Fee
                                  </SelectItem>
                                  <SelectItem value={EXPENSE_CATEGORIES.TRANSFER_FEE}>
                                    Transfer Fee
                                  </SelectItem>
                                  <SelectItem value={EXPENSE_CATEGORIES.WORKING_CAPITAL}>
                                    Working Capital
                                  </SelectItem>
                                  <SelectItem value={EXPENSE_CATEGORIES.OTHER}>
                                    Other
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        {/* Conditional fields based on category */}
                        {form.watch(`sellerExpenses.${index}.category`) === EXPENSE_CATEGORIES.DUES && (
                          <div className="mt-2">
                            <FormLabel className="text-sm font-medium mb-1 block">Dues Period</FormLabel>
                            <div className="flex items-center bg-white border rounded-md overflow-hidden">
                              <div className="flex-1 border-r">
                                <FormField
                                  control={form.control}
                                  name={`sellerExpenses.${index}.month`}
                                  render={({ field }) => (
                                    <Select
                                      onValueChange={field.onChange}
                                      defaultValue={field.value}
                                    >
                                      <FormControl>
                                        <SelectTrigger className="border-0 rounded-none shadow-none focus:ring-0 h-9">
                                          <SelectValue placeholder="Month" />
                                        </SelectTrigger>
                                      </FormControl>
                                      <SelectContent>
                                        {MONTHS.map((month) => (
                                          <SelectItem key={month} value={month}>
                                            {month}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                  )}
                                />
                              </div>
                              <div className="w-28">
                                <FormField
                                  control={form.control}
                                  name={`sellerExpenses.${index}.year`}
                                  render={({ field }) => (
                                    <Select
                                      onValueChange={field.onChange}
                                      defaultValue={field.value}
                                    >
                                      <FormControl>
                                        <SelectTrigger className="border-0 rounded-none shadow-none focus:ring-0 h-9">
                                          <SelectValue placeholder="Year" />
                                        </SelectTrigger>
                                      </FormControl>
                                      <SelectContent>
                                        {YEARS.map((year) => (
                                          <SelectItem key={year} value={year}>
                                            {year}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                  )}
                                />
                              </div>
                            </div>
                            <FormMessage />
                          </div>
                        )}

                        {form.watch(`sellerExpenses.${index}.category`) === EXPENSE_CATEGORIES.OTHER && (
                          <FormField
                            control={form.control}
                            name={`sellerExpenses.${index}.subcategory`}
                            render={({ field }) => (
                              <FormItem className="mt-2">
                                <FormLabel className="flex items-center">Description</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="e.g., Cleaning Fee"
                                    {...field}
                                    maxLength={50}
                                    className="bg-white"
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        )}
                      </div>

                      {/* Amount section - 5 columns */}
                      <div className="md:col-span-5">
                        <FormField
                          control={form.control}
                          name={`sellerExpenses.${index}.amount`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="flex items-center">
                                <span>Amount</span> <span className="text-red-500 ml-1">*</span>
                              </FormLabel>
                              <div className="relative">
                                <FormControl>
                                  <div className="relative">
                                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                                      $
                                    </span>
                                    <Input
                                      className="pl-7 bg-white [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                                      placeholder="0.00"
                                      type="number"
                                      step="0.01"
                                      onChange={(e) => {
                                        const value = e.target.value
                                          ? Number.parseFloat(e.target.value)
                                          : 0;
                                        field.onChange(value);
                                      }}
                                      value={field.value || ""}
                                    />
                                  </div>
                                </FormControl>
                              </div>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>



                      {/* Actions section - 2 columns */}
                      <div className="md:col-span-2 flex items-end justify-end">
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-500 border-red-200 hover:bg-red-50 hover:text-red-600"
                          type="button"
                          onClick={() => removeSeller(index)}
                          disabled={sellerFields.length <= 1}
                          title={sellerFields.length <= 1 ? "At least one expense is required" : "Remove this expense"}
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Remove
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}

                <Button
                  variant="outline"
                  className="w-full mb-4 border-dashed border-blue-200 text-blue-600 hover:bg-blue-50 hover:border-blue-300"
                  type="button"
                  onClick={() => appendSeller({ category: EXPENSE_CATEGORIES.OTHER, amount: 0, subcategory: "", month: "", year: "" })}
                >
                  <span className="mr-1">+</span> Add Seller Expense
                </Button>

                <div className="flex justify-between items-center bg-gray-50 p-3 rounded-md border">
                  <span className="font-medium text-gray-700">Seller Total:</span>
                  <span className="font-medium text-blue-600">${sellerTotal.toFixed(2)}</span>
                </div>
              </CardContent>
            </Card>

            {/* Buyer Expenses Card */}
            <Card className="border border-gray-200">
              <CardHeader className="pb-3 border-b border-gray-100">
                <div className="flex items-center">
                  <CardTitle className="text-xl text-gray-800">Buyer Expenses</CardTitle>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="ml-2 h-6 w-6"
                        >
                          <Info className="h-4 w-4 text-muted-foreground" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent className="p-3 max-w-sm">
                        <p className="text-sm">
                          Add expenses that will be paid by the buyer at settlement.
                          If no expenses are paid, select "Other" category, enter $0.00 as the amount,
                          and add a description like "No fees paid". Use the Notes field for additional details (up to 250 characters).
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </CardHeader>
              <CardContent>
                {buyerFields.map((field, index) => (
                  <div
                    key={field.id}
                    className="border rounded-md p-4 mb-4 bg-gray-50/50 relative"
                  >
                    {/* Row number indicator */}
                    <div className="absolute -left-2 -top-2 bg-blue-600 text-white w-6 h-6 rounded-full flex items-center justify-center text-sm font-medium shadow-sm">
                      {index + 1}
                    </div>

                    {/* Main content */}
                    <div className="grid grid-cols-1 md:grid-cols-12 gap-4">
                      {/* Category section - 5 columns */}
                      <div className="md:col-span-5">
                        <FormField
                          control={form.control}
                          name={`buyerExpenses.${index}.category`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="flex items-center">
                                <span>Category</span> <span className="text-red-500 ml-1">*</span>
                              </FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger className="bg-white">
                                    <SelectValue placeholder="Select category" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value={EXPENSE_CATEGORIES.DUES}>
                                    Dues
                                  </SelectItem>
                                  <SelectItem value={EXPENSE_CATEGORIES.SPECIAL_ASSESSMENT}>
                                    Special Assessment
                                  </SelectItem>
                                  <SelectItem value={EXPENSE_CATEGORIES.MOVE_IN_OUT_FEE}>
                                    Move In/Out Fee
                                  </SelectItem>
                                  <SelectItem value={EXPENSE_CATEGORIES.TRANSFER_FEE}>
                                    Transfer Fee
                                  </SelectItem>
                                  <SelectItem value={EXPENSE_CATEGORIES.WORKING_CAPITAL}>
                                    Working Capital
                                  </SelectItem>
                                  <SelectItem value={EXPENSE_CATEGORIES.OTHER}>
                                    Other
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        {/* Conditional fields based on category */}
                        {form.watch(`buyerExpenses.${index}.category`) === EXPENSE_CATEGORIES.DUES && (
                          <div className="mt-2">
                            <FormLabel className="text-sm font-medium mb-1 block">Dues Period</FormLabel>
                            <div className="flex items-center bg-white border rounded-md overflow-hidden">
                              <div className="flex-1 border-r">
                                <FormField
                                  control={form.control}
                                  name={`buyerExpenses.${index}.month`}
                                  render={({ field }) => (
                                    <Select
                                      onValueChange={field.onChange}
                                      defaultValue={field.value}
                                    >
                                      <FormControl>
                                        <SelectTrigger className="border-0 rounded-none shadow-none focus:ring-0 h-9">
                                          <SelectValue placeholder="Month" />
                                        </SelectTrigger>
                                      </FormControl>
                                      <SelectContent>
                                        {MONTHS.map((month) => (
                                          <SelectItem key={month} value={month}>
                                            {month}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                  )}
                                />
                              </div>
                              <div className="w-28">
                                <FormField
                                  control={form.control}
                                  name={`buyerExpenses.${index}.year`}
                                  render={({ field }) => (
                                    <Select
                                      onValueChange={field.onChange}
                                      defaultValue={field.value}
                                    >
                                      <FormControl>
                                        <SelectTrigger className="border-0 rounded-none shadow-none focus:ring-0 h-9">
                                          <SelectValue placeholder="Year" />
                                        </SelectTrigger>
                                      </FormControl>
                                      <SelectContent>
                                        {YEARS.map((year) => (
                                          <SelectItem key={year} value={year}>
                                            {year}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                  )}
                                />
                              </div>
                            </div>
                            <FormMessage />
                          </div>
                        )}

                        {form.watch(`buyerExpenses.${index}.category`) === EXPENSE_CATEGORIES.OTHER && (
                          <FormField
                            control={form.control}
                            name={`buyerExpenses.${index}.subcategory`}
                            render={({ field }) => (
                              <FormItem className="mt-2">
                                <FormLabel className="flex items-center">Description</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="e.g., Inspection Fee"
                                    {...field}
                                    maxLength={50}
                                    className="bg-white"
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        )}
                      </div>

                      {/* Amount section - 5 columns */}
                      <div className="md:col-span-5">
                        <FormField
                          control={form.control}
                          name={`buyerExpenses.${index}.amount`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="flex items-center">
                                <span>Amount</span> <span className="text-red-500 ml-1">*</span>
                              </FormLabel>
                              <div className="relative">
                                <FormControl>
                                  <div className="relative">
                                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                                      $
                                    </span>
                                    <Input
                                      className="pl-7 bg-white [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                                      placeholder="0.00"
                                      type="number"
                                      step="0.01"
                                      onChange={(e) => {
                                        const value = e.target.value
                                          ? Number.parseFloat(e.target.value)
                                          : 0;
                                        field.onChange(value);
                                      }}
                                      value={field.value || ""}
                                    />
                                  </div>
                                </FormControl>
                              </div>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>



                      {/* Actions section - 2 columns */}
                      <div className="md:col-span-2 flex items-end justify-end">
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-500 border-red-200 hover:bg-red-50 hover:text-red-600"
                          type="button"
                          onClick={() => removeBuyer(index)}
                          disabled={buyerFields.length <= 1}
                          title={buyerFields.length <= 1 ? "At least one expense is required" : "Remove this expense"}
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Remove
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}

                <Button
                  variant="outline"
                  className="w-full mb-4 border-dashed border-blue-200 text-blue-600 hover:bg-blue-50 hover:border-blue-300"
                  type="button"
                  onClick={() => appendBuyer({ category: EXPENSE_CATEGORIES.OTHER, amount: 0, subcategory: "", month: "", year: "" })}
                >
                  <span className="mr-1">+</span> Add Buyer Expense
                </Button>

                <div className="flex justify-between items-center bg-gray-50 p-3 rounded-md border">
                  <span className="font-medium text-gray-700">Buyer Total:</span>
                  <span className="font-medium text-blue-600">${buyerTotal.toFixed(2)}</span>
                </div>
              </CardContent>
            </Card>

            {/* Special Notes Card */}
            <Card className="border border-gray-200">
              <CardHeader className="pb-3 border-b border-gray-100">
                <CardTitle className="text-xl text-gray-800">Special Notes</CardTitle>
              </CardHeader>
              <CardContent>
                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Textarea
                          placeholder="Add any special notes or additional information about this settlement"
                          className="resize-none bg-white"
                          {...field}
                          maxLength={250}
                        />
                      </FormControl>
                      <FormMessage />
                      <div className="text-xs text-muted-foreground text-right mt-1">
                        {field.value?.length || 0}/250 characters
                      </div>
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Submit Button Card */}
            <Card className="border border-gray-200">
              <CardContent className="py-5">
                <div className="flex justify-end">
                  <Button
                    size={"lg"}
                    type="submit"
                    disabled={
                      submitting || organization.loading || !!organization.error
                    }
                    className="bg-blue-600 hover:bg-blue-700 py-5 px-8 text-base rounded-lg"
                  >
                    {submitting ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Submitting...
                      </>
                    ) : (
                      <>
                        Submit Settlement Details (${totalSettlement.toFixed(2)})
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </>
        )}
      </form>
    </Form>
  );
}
