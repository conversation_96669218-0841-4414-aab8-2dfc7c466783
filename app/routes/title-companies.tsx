import { ArrowLeft, ExternalLink, Info, AlertTriangle, CheckCircle } from "lucide-react";
import { Logo } from "~/components/logo";
import { Alert, AlertDescription, AlertTitle } from "~/components/ui/alert";
import { Button } from "~/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import { Input } from "~/components/ui/input";
import { Link, useNavigate } from "react-router";
import { useState, useEffect } from "react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "~/components/ui/tooltip";

export function meta() {
  return [
    { title: "Title Companies - TitleSync" },
    {
      name: "description",
      content:
        "Submit settlement details with TitleSync - the streamlined HOA settlement process",
    },
  ];
}

export default function TitleCompaniesPage() {
  const [settlementUrl, setSettlementUrl] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [isValidUrl, setIsValidUrl] = useState(false);
  const navigate = useNavigate();

  // Validate URL when it changes
  useEffect(() => {
    if (!settlementUrl) {
      setIsValidUrl(false);
      setError(null);
      return;
    }

    // Clean up the URL if needed
    let url = settlementUrl.trim();

    try {
      // Try to extract an organization slug from the input
      let orgSlug = "";

      // Case 1: Full URL (e.g., https://www.title-sync.com/clayton-property-demo)
      if (url.startsWith('http://') || url.startsWith('https://')) {
        try {
          const parsedUrl = new URL(url);
          const pathParts = parsedUrl.pathname.split('/').filter(Boolean);
          if (pathParts.length > 0) {
            orgSlug = pathParts[0];
          }
        } catch (e) {
          // Invalid URL format, try other methods
        }
      }

      // Case 2: Domain with path (e.g., www.title-sync.com/clayton-property-demo)
      else if (url.includes('/')) {
        const parts = url.split('/').filter(Boolean);
        if (parts.length > 1) {
          orgSlug = parts[1]; // First part is domain, second is slug
        }
      }

      // Case 3: Just the slug (e.g., clayton-property-demo)
      else if (url.length > 0) {
        orgSlug = url;
      }

      // If we found a slug, consider it valid
      if (orgSlug && orgSlug.length > 0) {
        console.log("Found organization slug:", orgSlug);
        setIsValidUrl(true);
        setError(null);
      } else {
        setIsValidUrl(false);
        setError("Please enter a valid organization identifier.");
      }
    } catch (e) {
      console.error("URL validation error:", e);
      setIsValidUrl(false);
      setError("Please enter a valid URL or organization identifier.");
    }
  }, [settlementUrl]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!isValidUrl) {
      setError("Please enter a valid organization identifier.");
      return;
    }

    try {
      // Extract the organization slug using the same logic as in the validation
      let url = settlementUrl.trim();
      let orgSlug = "";

      // Case 1: Full URL (e.g., https://www.title-sync.com/clayton-property-demo)
      if (url.startsWith('http://') || url.startsWith('https://')) {
        try {
          const parsedUrl = new URL(url);
          const pathParts = parsedUrl.pathname.split('/').filter(Boolean);
          if (pathParts.length > 0) {
            orgSlug = pathParts[0];
          }
        } catch (e) {
          // Invalid URL format, try other methods
        }
      }

      // Case 2: Domain with path (e.g., www.title-sync.com/clayton-property-demo)
      else if (url.includes('/')) {
        const parts = url.split('/').filter(Boolean);
        if (parts.length > 1) {
          orgSlug = parts[1]; // First part is domain, second is slug
        }
      }

      // Case 3: Just the slug (e.g., clayton-property-demo)
      else if (url.length > 0) {
        orgSlug = url;
      }

      if (orgSlug && orgSlug.length > 0) {
        // Make sure the slug doesn't contain any invalid characters
        const cleanSlug = orgSlug.replace(/[^a-zA-Z0-9-_]/g, '');

        if (cleanSlug !== orgSlug) {
          console.log("Cleaned organization slug:", cleanSlug, "Original:", orgSlug);
          setError(`Organization name contains invalid characters. Using: ${cleanSlug}`);
          orgSlug = cleanSlug;
        }

        if (orgSlug.length === 0) {
          setError("Invalid organization name. Please enter a valid organization identifier.");
          return;
        }

        console.log("Navigating to organization slug:", orgSlug);

        // Show a success message instead of an error
        setError(null);

        // Use a timeout to ensure the message is displayed before navigation
        setTimeout(() => {
          // Use navigate instead of window.location for client-side routing
          navigate(`/${orgSlug}`);
        }, 500);
      } else {
        setError("Invalid format. Please enter a valid organization identifier.");
      }
    } catch (e) {
      console.error("Navigation error:", e);
      setError("Please enter a valid organization identifier.");
    }
  };

  return (
    <div className="flex flex-col items-center justify-center py-8 px-4 sm:px-6">
      <Card className="w-full max-w-sm sm:max-w-md shadow-lg bg-white rounded-2xl border border-gray-200">
        <CardHeader className="flex flex-col items-center space-y-3 pb-4 px-6 sm:px-8 pt-6 sm:pt-8 border-b border-gray-100">
          <CardTitle className="text-2xl md:text-3xl text-center font-semibold">
            Title Company Portal
          </CardTitle>
          <CardDescription className="text-center text-gray-500 text-sm">
            Submit settlement details for property transactions
          </CardDescription>
        </CardHeader>
        <CardContent className="px-6 sm:px-8 pb-6 pt-5 space-y-6">
          <form onSubmit={handleSubmit}>
            <div className="space-y-5">
              <div className="flex items-center pb-1">
                <h3 className="font-medium text-lg text-gray-700">Create a Settlement</h3>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="ghost" size="icon" className="ml-2 h-6 w-6">
                        <Info className="h-4 w-4 text-blue-500" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent className="max-w-xs">
                      <p className="text-sm">
                        Enter the organization name or URL provided by the property manager.
                        You can enter just the organization name (e.g., "clayton-property-demo"),
                        or the full URL (e.g., "www.title-sync.com/clayton-property-demo").
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              <div className="space-y-3">
                <label htmlFor="settlement-url" className="text-sm font-medium text-gray-700">
                  Enter organization name or settlement link
                </label>
                <Input
                  id="settlement-url"
                  type="text"
                  placeholder="Enter organization name or URL (e.g., clayton-property-demo)"
                  value={settlementUrl}
                  onChange={(e) => setSettlementUrl(e.target.value)}
                  className={`w-full py-4 px-4 text-base border rounded-lg shadow-sm ${error ? 'border-red-500' : ''}`}
                  required
                />
                {error && (
                  <div className="flex items-center text-red-500 text-sm mt-1">
                    <AlertTriangle className="h-4 w-4 mr-1" />
                    <span>{error}</span>
                  </div>
                )}
                {isValidUrl && (
                  <div className="flex items-center text-green-600 text-sm mt-1">
                    <CheckCircle className="h-4 w-4 mr-1" />
                    <span>Organization found. Ready to proceed.</span>
                  </div>
                )}
              </div>

              <Button
                type="submit"
                className="w-full py-4 bg-blue-600 hover:bg-blue-700 transition-all flex items-center justify-center gap-3 text-base shadow-sm rounded-lg mt-4"
                disabled={!isValidUrl}
                onClick={() => console.log("Continue button clicked")}
              >
                Continue to Settlement Form
              </Button>
            </div>
          </form>
        </CardContent>
        <CardFooter className="flex justify-center pb-6 pt-2 border-t border-gray-100 mt-4">
          <p className="text-xs text-muted-foreground opacity-70">
            © 2025 TitleSync
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}
