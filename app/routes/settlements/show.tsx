import { useState } from "react";
import { Container } from "~/components/container";
import { But<PERSON> } from "~/components/ui/button";
import { SettlementStatusBadge } from "~/api/settlements/ui/settlement-status-badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import { Separator } from "~/components/ui/separator";
import { DebugData } from "~/components/debug";
import { ResourceId } from "~/components/resource-id";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "~/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "~/components/ui/dialog";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import {
  ChevronLeft,
  Calendar,
  Building,
  Mail,
  DollarSign,
  Clock,
  CreditCard,
  FileText,
  ExternalLink,
  Download,
  Pencil,
} from "lucide-react";
import { FileViewer } from "~/components/file-viewer";
import { getFileUrl } from "~/utils/file-utils";
import { EXPENSE_CATEGORIES, getCategoryLabel, type ExpenseCategory } from "~/api/settlements/expense-categories";
import type { Route } from "./+types/show";
import { useSettlement, useUpdateSettlement } from "~/api/settlements/settlements-hooks";
import { Link } from "react-router";
import { Loading } from "~/components/loading";

// Helper function to format currency
const formatCurrency = (amount: number | null | undefined) => {
  if (amount === null || amount === undefined) return "--";
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
  }).format(amount);
};

// Helper function to calculate total expenses
const calculateTotal = (
  expenses:
    | ReadonlyArray<{
      readonly category: string;
      readonly amount: number;
      readonly subcategory?: string;
      readonly month?: string;
      readonly year?: string;
    }>
    | undefined,
) => {
  if (!expenses || expenses.length === 0) return 0;
  return expenses.reduce((sum, expense) => sum + (expense.amount || 0), 0);
};

// Helper function to format expense category display
const formatExpenseCategory = (expense: {
  readonly category: string;
  readonly amount: number;
  readonly subcategory?: string;
  readonly month?: string;
  readonly year?: string;
}) => {
  const categoryLabel = getCategoryLabel(expense.category as ExpenseCategory);

  if (expense.category === EXPENSE_CATEGORIES.DUES && expense.month && expense.year) {
    return `${categoryLabel} (${expense.month} ${expense.year})`;
  }

  if (expense.category === EXPENSE_CATEGORIES.OTHER && expense.subcategory) {
    return `${categoryLabel}: ${expense.subcategory}`;
  }

  return categoryLabel;
};

// Helper function to format dates
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  }).format(date);
};

// Property Details Edit Form Component
interface PropertyDetailsEditFormProps {
  settlement: any; // Using any for now, should be properly typed
}

function PropertyDetailsEditForm({ settlement }: PropertyDetailsEditFormProps) {
  const [propertyAddress, setPropertyAddress] = useState(settlement.property_address || "");
  const [unit, setUnit] = useState(settlement.unit || "");
  const [hoaName, setHoaName] = useState(settlement.hoa_name || "");
  const [buyerEmail, setBuyerEmail] = useState(settlement.buyer_email || "");

  // Use the update settlement mutation
  const updateSettlement = useUpdateSettlement(settlement.id);
  const isSubmitting = updateSettlement.isPending;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // Call the API to update the settlement
      await updateSettlement.mutateAsync({
        property_address: propertyAddress,
        unit: unit,
        hoa_name: hoaName,
        buyer_email: buyerEmail
      });

      // Refresh the page to show updated data
      window.location.reload();
    } catch (error) {
      console.error("Error updating settlement:", error);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="grid gap-4 py-4">
        <div className="grid gap-2">
          <Label htmlFor="propertyAddress">Property Address</Label>
          <Input
            id="propertyAddress"
            value={propertyAddress}
            onChange={(e) => setPropertyAddress(e.target.value)}
            required
          />
        </div>
        <div className="grid gap-2">
          <Label htmlFor="unit">Unit Number</Label>
          <Input
            id="unit"
            value={unit}
            onChange={(e) => setUnit(e.target.value)}
            placeholder="Optional"
          />
        </div>
        <div className="grid gap-2">
          <Label htmlFor="hoaName">HOA Name</Label>
          <Input
            id="hoaName"
            value={hoaName}
            onChange={(e) => setHoaName(e.target.value)}
            placeholder="Optional"
          />
        </div>
        <div className="grid gap-2">
          <Label htmlFor="buyerEmail">Buyer Email</Label>
          <Input
            id="buyerEmail"
            type="email"
            value={buyerEmail}
            onChange={(e) => setBuyerEmail(e.target.value)}
            placeholder="Optional"
          />
        </div>
      </div>
      <DialogFooter>
        <DialogClose asChild>
          <Button type="button" variant="outline">Cancel</Button>
        </DialogClose>
        <Button
          type="submit"
          disabled={isSubmitting}
          className="shadow-sm font-medium bg-sky-600 hover:bg-sky-700 text-white rounded-lg"
        >
          {isSubmitting ? "Saving..." : "Save Changes"}
        </Button>
      </DialogFooter>
    </form>
  );
}

export default function SettlementDetailsPage({
  params,
}: Route.ComponentProps) {
  const settlement = useSettlement(params.id);
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);

  if (settlement.isLoading) {
    return <Loading />;
  }

  if (!settlement.data) {
    return <div className="text-red-500">Settlement not found</div>;
  }

  const warrantyDeedKey = settlement.data.warranty_deed_key;

  // Calculate total amount
  const totalAmount =
    calculateTotal(settlement.data.seller_expenses) +
    calculateTotal(settlement.data.buyer_expenses);

  return (
    <>
      <div className="mb-6">
        <Link
          to="/settlements"
          className="flex items-center gap-1.5 text-sm text-gray-600 hover:text-gray-900 transition-colors py-1"
        >
          <ChevronLeft className="h-4 w-4" /> Back to settlements
        </Link>
      </div>

      <div className="grid gap-6 items-start">
        {/* Property Details */}
        <Card className="shadow-md">
          <CardHeader className="pb-2 border-b border-gray-100 bg-gray-50/50">
            <div className="flex justify-between items-center">
              <div>
                <CardTitle className="text-lg">Property Details</CardTitle>
              </div>
              <div className="flex gap-2">
                <Dialog>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            className="shadow-sm border-gray-200 hover:border-gray-300 hover:bg-gray-50 rounded-lg"
                          >
                            <Pencil className="h-4 w-4 text-gray-500" />
                          </Button>
                        </DialogTrigger>
                      </TooltipTrigger>
                      <TooltipContent side="top">
                        Edit property details
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                      <DialogTitle>Edit Property Details</DialogTitle>
                      <DialogDescription>
                        Update the property information
                      </DialogDescription>
                    </DialogHeader>
                    <PropertyDetailsEditForm settlement={settlement.data} />
                  </DialogContent>
                </Dialog>
                <Button
                  variant="default"
                  size="sm"
                  className="shadow-sm font-medium bg-sky-600 hover:bg-sky-700 text-white rounded-lg"
                >
                  Generate New Owner
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-3 grid gap-3">
            <div>
              <div className="flex items-center gap-1">
                <h2 className="text-xl font-bold">
                  {settlement.data.name || settlement.data.property_address}
                </h2>
              </div>
              <div className="flex items-center gap-1 font-medium text-muted-foreground mt-1">
                {settlement.data.unit && (
                  <span>Unit {settlement.data.unit}</span>
                )}
              </div>
            </div>
            {settlement.data.hoa_name && (
              <div>
                <div className="text-sm font-medium text-gray-500 mb-1">
                  Homeowner Association
                </div>
                <div className="flex items-center gap-2">
                  <Building className="h-4 w-4 text-gray-500" />
                  <span>{settlement.data.hoa_name}</span>
                </div>
              </div>
            )}
            {settlement.data.buyer_email && (
              <div>
                <div className="text-sm font-medium text-gray-500 mb-1">
                  Buyer Email
                </div>
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span className="break-all">{settlement.data.buyer_email}</span>
                </div>
              </div>
            )}

            {/* Warranty Deed */}
            {warrantyDeedKey && (
              <>
                <Separator className="my-2" />
                <div>
                  <div className="text-sm font-medium text-gray-500 mb-1">
                    Warranty Deed
                  </div>
                  <div className="flex items-center justify-between gap-2 p-2 border border-gray-200 rounded-md bg-white">
                    <div className="flex items-center gap-2 min-w-0">
                      <div className="flex-shrink-0 p-1.5 bg-gray-100 rounded-md">
                        <FileText className="h-4 w-4 text-gray-500" />
                      </div>
                      <div className="min-w-0 flex-1">
                        <p className="text-sm font-medium truncate">
                          {warrantyDeedKey.split('/').pop() || 'warranty-deed.pdf'}
                        </p>
                      </div>
                    </div>
                    <div className="flex gap-2 flex-shrink-0">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const url = getFileUrl(warrantyDeedKey);

                          if (!url) {
                            alert("No file available");
                            return;
                          }

                          return window.open(url, '_blank');
                        }}
                        className="shadow-sm h-7 px-2"
                      >
                        <ExternalLink className="w-3.5 h-3.5" />
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const link = document.createElement('a');
                          link.href = getFileUrl(warrantyDeedKey) || '';
                          link.download = warrantyDeedKey.split('/').pop() || 'warranty-deed.pdf';
                          document.body.appendChild(link);
                          link.click();
                          document.body.removeChild(link);
                        }}
                        className="shadow-sm h-7 px-2"
                      >
                        <Download className="w-3.5 h-3.5" />
                      </Button>
                    </div>
                  </div>
                </div>
              </>
            )}

            {/* Transaction Metadata */}
            <Separator className="my-2" />
            <div className="flex flex-wrap gap-x-8 gap-y-2 text-xs text-muted-foreground mt-2">
              <div className="flex items-center gap-2">
                <Calendar className="h-3.5 w-3.5 text-gray-400" />
                <span>Created {formatDate(settlement.data.created_at)}</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-3.5 w-3.5 text-gray-400" />
                <span>Updated {formatDate(settlement.data.updated_at)}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-400">ID:</span>
                <ResourceId id={settlement.data.id} truncate={true} maxLength={8} className="text-muted-foreground" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Settlement Details */}
        <Card className="shadow-md">
          <CardHeader className="pb-2 border-b border-gray-100 bg-gray-50/50">
            <CardTitle className="text-lg">Settlement Details</CardTitle>
          </CardHeader>
          <CardContent className="pt-3">
            {/* Payment method information could go here in the future */}

            <Separator className="my-3" />
            {settlement.data.seller_expenses &&
              settlement.data.seller_expenses.length > 0 ? (
              <div className="mb-3">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Seller Expenses</h4>
                <div className="space-y-2">
                  {settlement.data.seller_expenses.map((expense, index) => (
                    <div
                      key={`seller-expense-${index}-${expense.category}`}
                      className="flex justify-between items-center text-sm"
                    >
                      <span className="text-gray-700">{formatExpenseCategory(expense)}</span>
                      <span className="font-medium">
                        {formatCurrency(expense.amount)}
                      </span>
                    </div>
                  ))}

                  <div className="flex justify-between items-center text-sm pt-3 mt-1 border-t border-gray-100">
                    <span className="font-medium text-gray-700">Subtotal</span>
                    <span className="font-medium">
                      {formatCurrency(
                        calculateTotal(settlement.data.seller_expenses),
                      )}
                    </span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="mb-3">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Seller Expenses</h4>
                <p className="text-sm text-muted-foreground italic">
                  No seller expenses recorded
                </p>
              </div>
            )}

            <Separator className="my-3" />

            {settlement.data.buyer_expenses &&
              settlement.data.buyer_expenses.length > 0 ? (
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Buyer Expenses</h4>
                <div className="space-y-2">
                  {settlement.data.buyer_expenses.map((expense, index) => (
                    <div
                      key={`buyer-expense-${index}-${expense.category}`}
                      className="flex justify-between items-center text-sm"
                    >
                      <span className="text-gray-700">{formatExpenseCategory(expense)}</span>
                      <span className="font-medium">
                        {formatCurrency(expense.amount)}
                      </span>
                    </div>
                  ))}

                  <div className="flex justify-between items-center text-sm pt-3 mt-1 border-t border-gray-100">
                    <span className="font-medium text-gray-700">Subtotal</span>
                    <span className="font-medium">
                      {formatCurrency(
                        calculateTotal(settlement.data.buyer_expenses),
                      )}
                    </span>
                  </div>
                </div>
              </div>
            ) : (
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-3">Buyer Expenses</h4>
                <p className="text-sm text-muted-foreground italic">
                  No buyer expenses recorded
                </p>
              </div>
            )}

            {/* Special Notes */}
            {settlement.data.notes && (
              <>
                <Separator className="my-3" />
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Special Notes</h4>
                  <div className="text-sm whitespace-pre-wrap text-gray-700">
                    {settlement.data.notes}
                  </div>
                </div>
              </>
            )}

            {/* Grand Total */}
            {((settlement.data.seller_expenses &&
              settlement.data.seller_expenses.length > 0) ||
              (settlement.data.buyer_expenses &&
                settlement.data.buyer_expenses.length > 0)) && (
                <>
                  <Separator className="my-3" />
                  <div className="flex justify-between items-center text-base font-medium">
                    <span className="text-gray-800">Grand Total</span>
                    <div className="flex items-center gap-2">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span>
                              <SettlementStatusBadge
                                status={settlement.data.payment_status}
                                size="sm"
                              />
                            </span>
                          </TooltipTrigger>
                          <TooltipContent side="top">
                            {settlement.data.payment_status === "pending"
                              ? "Click 'Check Received' when payment arrives"
                              : settlement.data.payment_status === "paid"
                                ? "Payment has been received and processed"
                                : ""}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                      <span className="text-gray-900">
                        {formatCurrency(
                          calculateTotal(settlement.data.seller_expenses) +
                          calculateTotal(settlement.data.buyer_expenses),
                        )}
                      </span>
                    </div>
                  </div>
                </>
              )}
          </CardContent>
        </Card>
      </div >

      <div className="mt-8 flex justify-end gap-4">
        {settlement.data.payment_status === "pending" && (
          <>
            <Button
              variant="outline"
              size="default"
              className="shadow-sm font-medium border-sky-600 text-sky-600 hover:bg-sky-50 rounded-lg"
              onClick={() => setIsPaymentModalOpen(true)}
            >
              <CreditCard className="h-4 w-4 mr-2" />
              Submit Payment
            </Button>
            <Button
              variant="default"
              size="default"
              className="shadow-sm font-medium bg-sky-600 hover:bg-sky-700 text-white rounded-lg"
            >
              Check Received
            </Button>
          </>
        )}

        {/* Payment Modal */}
        {settlement.data && (
          <PaymentModal
            isOpen={isPaymentModalOpen}
            onClose={() => setIsPaymentModalOpen(false)}
            settlementId={settlement.data.id}
            amount={totalAmount}
            onPaymentSuccess={() => {
              // Refresh the settlement data after successful payment
              settlement.refetch();
            }}
          />
        )}
      </div>

      <div className="flex justify-center mt-8 pt-4 border-t border-gray-100">
        <p className="text-xs text-muted-foreground">
          © 2025 TitleSync
        </p>
      </div>
    </>
  );
}
