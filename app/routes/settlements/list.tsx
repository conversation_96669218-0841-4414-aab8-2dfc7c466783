import { useState, useMemo, useRef } from "react";
import { useOrganization } from "@clerk/react-router";
import { LayoutGrid, List } from "lucide-react";

import { useSettlements, type SettlementFilters } from "~/api/settlements/settlements-hooks";
import { Container } from "~/components/container";
import { DebugData } from "~/components/debug";
import SettlementsTable from "~/api/settlements/ui/settlements-table";
import GroupedSettlements from "~/api/settlements/ui/grouped-settlements";
import { Button } from "~/components/ui/button";
import { Check, Copy, ExternalLink, Mail, Link as LinkIcon, Plus, Info } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "~/components/ui/card";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "~/components/ui/tooltip";
import { Loading } from "~/components/loading";
import { SearchInput } from "~/components/search-input";
import { StatusFilter } from "~/components/status-filter";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Separator } from "~/components/ui/separator";
import { ToggleGroup, ToggleGroupItem } from "~/components/ui/toggle-group";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetFooter,
  SheetClose,
} from "~/components/ui/sheet";

// Define the settlement data type
interface ExpenseItem {
  category: string;
  amount: number;
}

interface SettlementItem {
  id: string;
  name?: string;
  organization_id: string;
  property_address: string;
  unit?: string;
  hoa_name?: string;
  buyer_email?: string;
  warranty_deed_key?: string;
  amount?: number;
  payment_status: string;
  seller_expenses?: ExpenseItem[];
  buyer_expenses?: ExpenseItem[];
  total_amount?: number;
  created_at: string;
  updated_at: string;
}

interface SettlementsData {
  items: SettlementItem[];
  count: number;
}



export default function SettlementsListPage() {
  const [searchFilter, setSearchFilter] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [titleCompanyEmail, setTitleCompanyEmail] = useState("");
  const [isCopied, setIsCopied] = useState(false);
  const [viewMode, setViewMode] = useState<"grouped" | "list">("grouped");
  const organization = useOrganization();
  const urlInputRef = useRef<HTMLInputElement>(null);

  // Create filters object for the API
  const filters: SettlementFilters = useMemo(() => {
    const result: SettlementFilters = {};
    if (searchFilter) result.search = searchFilter;
    if (statusFilter && statusFilter !== "all") result.status = statusFilter;
    return result;
  }, [searchFilter, statusFilter]);

  // Fetch settlements with filters
  const { data: settlementsData, isLoading, error } = useSettlements(filters) as {
    data: SettlementsData | undefined;
    isLoading: boolean;
    error: unknown;
  };

  // Get the organization ID from Clerk
  const orgId = organization.organization?.id;
  const orgName = organization.organization?.name;
  const orgSlug = organization.organization?.slug;

  // Generate the title company URL
  const titleCompanyUrl = useMemo(() => {
    return `https://www.title-sync.com/${orgSlug}`;
  }, [orgSlug]);

  // Function to copy URL to clipboard
  const copyToClipboard = () => {
    if (urlInputRef.current) {
      urlInputRef.current.select();
      document.execCommand('copy');
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    }
  };

  // Function to send email to title company
  const sendEmailToTitleCompany = () => {
    // This would typically integrate with an email service
    // For now, we'll just log the action and close the sheet
    console.log(`Email would be sent to: ${titleCompanyEmail}`);
    alert(`An invitation would be sent to ${titleCompanyEmail}`);
    setTitleCompanyEmail("");
    setIsSheetOpen(false);
  };

  // Extract unique statuses from settlements data
  const availableStatuses = useMemo(() => {
    if (!settlementsData || !Array.isArray(settlementsData.items)) return [];
    const statuses = new Set<string>();
    for (const settlement of settlementsData.items) {
      if (settlement.payment_status) {
        statuses.add(settlement.payment_status);
      }
    }
    return Array.from(statuses);
  }, [settlementsData]);

  return (
    <>
      {/* Organization Title */}
      <div className="mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
          {orgName || "Property Management Dashboard"}
        </h1>
        <p className="text-gray-600 mt-2 text-sm sm:text-base">
          Manage your settlements and property documents
        </p>
      </div>

      {/* Search and filter controls */}
      <div className="mb-6 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 sm:gap-0">
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 w-full sm:w-auto">
          <SearchInput
            placeholder="Search settlements..."
            value={searchFilter}
            onChange={setSearchFilter}
            className="w-full sm:w-[280px]"
          />
          {!isLoading && !error && availableStatuses.length > 0 && (
            <StatusFilter
              statuses={availableStatuses}
              value={statusFilter}
              onChange={setStatusFilter}
              className="bg-background w-full sm:w-auto"
            />
          )}

          {/* View toggle */}
          <div className="ml-0 sm:ml-2 mt-2 sm:mt-0">
            <ToggleGroup type="single" value={viewMode} onValueChange={(value) => value && setViewMode(value as "grouped" | "list")}>
              <ToggleGroupItem value="grouped" aria-label="Group by HOA" title="Group by HOA">
                <LayoutGrid className="h-4 w-4" />
              </ToggleGroupItem>
              <ToggleGroupItem value="list" aria-label="List view" title="List view">
                <List className="h-4 w-4" />
              </ToggleGroupItem>
            </ToggleGroup>
          </div>
        </div>
        <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
          <SheetTrigger asChild>
            <Button
              aria-label="Initiate Settlement"
              className="w-full sm:w-auto px-4 py-2 bg-sky-600 hover:bg-sky-700 transition-all flex items-center justify-center gap-2 text-base shadow-sm rounded-lg"
            >
              <Plus className="h-4 w-4" />
              <span>Initiate Settlement</span>
            </Button>
          </SheetTrigger>
          <SheetContent className="sm:max-w-md">
            <SheetHeader className="pb-4">
              <SheetTitle className="text-xl">Initiate Settlement</SheetTitle>
              <SheetDescription className="text-base">
                Invite a title company to submit settlement details
              </SheetDescription>
            </SheetHeader>
            <div className="px-6">
              <div className="grid gap-8 py-6">
                <div className="space-y-4">
                  <div className="flex items-center">
                    <h3 className="text-base font-medium text-gray-700">Share Settlement Link</h3>
                  </div>
                  <div className="grid gap-4">
                    <Label htmlFor="settlement-url" className="text-sm font-medium text-gray-700">Settlement URL</Label>
                    <div className="flex gap-2">
                      <Input
                        id="settlement-url"
                        readOnly
                        value={titleCompanyUrl}
                        ref={urlInputRef}
                        className="font-mono text-sm shadow-sm border rounded-lg"
                      />
                      <Button
                        onClick={copyToClipboard}
                        className="bg-gray-600 hover:bg-gray-700 shadow-sm rounded-lg"
                      >
                        {isCopied ? (
                          <>
                            <Check className="h-4 w-4 mr-2" />
                            Copied
                          </>
                        ) : (
                          <>
                            <Copy className="h-4 w-4 mr-2" />
                            Copy
                          </>
                        )}
                      </Button>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      <p>
                        Copy this unique URL and share it with the title company via email to allow them to submit settlement details directly to you.
                      </p>
                    </div>
                  </div>
                </div>

                <Separator className="my-2" />

                <div className="space-y-4">
                  <div className="flex items-center">
                    <h3 className="text-base font-medium text-gray-700">Send Email Invitation</h3>
                  </div>
                  <div className="grid gap-4">
                    <Label htmlFor="title-company-email" className="text-sm font-medium text-gray-700">Title Company Email</Label>
                    <div className="flex gap-2">
                      <Input
                        id="title-company-email"
                        placeholder="<EMAIL>"
                        value={titleCompanyEmail}
                        onChange={(e) => setTitleCompanyEmail(e.target.value)}
                        type="email"
                        className="shadow-sm border rounded-lg"
                      />
                      <Button
                        onClick={sendEmailToTitleCompany}
                        disabled={!titleCompanyEmail || !titleCompanyEmail.includes('@')}
                        className="bg-gray-600 hover:bg-gray-700 shadow-sm rounded-lg"
                      >
                        <Mail className="h-4 w-4 mr-2" />
                        Send
                      </Button>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      <p>
                        Send an email invitation to the title company with a link to submit settlement details.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <SheetFooter className="pt-4 pb-2 border-t border-gray-100 mt-4">
                <SheetClose asChild>
                  <Button variant="outline" className="w-full sm:w-auto shadow-sm rounded-lg">Close</Button>
                </SheetClose>
              </SheetFooter>
            </div>
          </SheetContent>
        </Sheet>
      </div>

      {
        isLoading ? (
          <div className="py-4" >
            <Loading />
          </div>
        ) : error ? (
          <div className="text-red-500 py-4">Error loading settlements</div>
        ) : (
          <>
            {settlementsData && Array.isArray(settlementsData.items) && (
              viewMode === "grouped" ? (
                <GroupedSettlements
                  settlements={settlementsData}
                />
              ) : (
                <SettlementsTable
                  settlements={settlementsData}
                />
              )
            )}
          </>
        )
      }

      <div className="flex justify-center mt-8 sm:mt-12 pt-4 sm:pt-6 border-t border-gray-100">
        <p className="text-xs text-muted-foreground opacity-70">
          © 2025 TitleSync
        </p>
      </div>
    </>
  );
}
