import {
  type RouteConfig,
  index,
  layout,
  route,
} from "@react-router/dev/routes";

export default [
  index("routes/home.tsx"),
  route("login", "routes/login.tsx"),
  // route("auth", "routes/auth/index.tsx"),

  layout("routes/title-companies/layout.tsx", [
    route("title-companies", "routes/title-companies.tsx"),
  ]),

  layout("routes/title-company/layout.tsx", [
    route("title-company/:org_slug", "routes/title-company.$org_slug.tsx"),
    route(":org_slug", "routes/$org_slug.tsx"),
  ]),

  // Success page with its own layout (no white container)
  route("title-company-success", "routes/title-company/success.tsx"),

  // route("settlements/form", "routes/settlements/form.tsx"),

  layout("routes/dashboard/layout.tsx", [
    // Settlements routes
    route("settlements", "routes/settlements/list.tsx"),
    route("settlements/:id", "routes/settlements/show.tsx"),
    route("homeowner-associations", "routes/homeowner-associations/list.tsx"),
    route(
      "homeowner-associations/:id",
      "routes/homeowner-associations/show.tsx",
    ),
  ]),
] satisfies RouteConfig;
