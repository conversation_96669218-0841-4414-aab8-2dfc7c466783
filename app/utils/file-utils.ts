/**
 * Extracts the file ID from a full file key
 * @param key The full file key (e.g., "uploads/file_123abc")
 * @returns The file ID portion (e.g., "file_123abc")
 */
export function extractFileId(key: string | null | undefined): string | null {
  if (!key) return null;
  
  // If the key includes a path (like "uploads/file_123abc"), extract just the ID
  const parts = key.split('/');
  return parts[parts.length - 1] || null;
}

/**
 * Generates a URL for accessing an uploaded file
 * @param key The file key or ID
 * @returns A URL to access the file, or null if no key provided
 */
export function getFileUrl(key: string | null | undefined): string | null {
  if (!key) return null;
  
  // If it's already a full key with a path, extract just the ID
  const fileId = extractFileId(key);
  if (!fileId) return null;
  
  // Generate the URL to the file
  return `/uploads/${fileId}`;
}

/**
 * Determines if a file is an image based on its key or URL
 * @param key The file key or URL
 * @returns True if the file appears to be an image
 */
export function isImageFile(key: string | null | undefined): boolean {
  if (!key) return false;
  
  const lowerKey = key.toLowerCase();
  return lowerKey.endsWith('.jpg') || 
         lowerKey.endsWith('.jpeg') || 
         lowerKey.endsWith('.png') || 
         lowerKey.endsWith('.gif') || 
         lowerKey.endsWith('.webp');
}

/**
 * Determines if a file is a PDF based on its key or URL
 * @param key The file key or URL
 * @returns True if the file appears to be a PDF
 */
export function isPdfFile(key: string | null | undefined): boolean {
  if (!key) return false;
  
  return key.toLowerCase().endsWith('.pdf');
}
