import * as HttpApiEndpoint from "@effect/platform/HttpApiEndpoint";
import * as HttpApiGroup from "@effect/platform/HttpApiGroup";
import * as Schema from "effect/Schema";
import { Authentication } from "../authentication";

// Reusable schemas
export const PaymentMethodTypeSchema = Schema.Union(
  Schema.Literal("ach_debit"),
  Schema.Literal("card")
);

export const PaymentIntentStatusSchema = Schema.Union(
  Schema.Literal("requires_payment_method"),
  Schema.Literal("requires_confirmation"),
  Schema.Literal("requires_action"),
  Schema.Literal("processing"),
  Schema.Literal("requires_capture"),
  Schema.Literal("canceled"),
  Schema.Literal("succeeded")
);

export const PaymentIntentSchema = Schema.Struct({
  id: Schema.String,
  client_secret: Schema.String,
  amount: Schema.Number,
  currency: Schema.String,
  status: PaymentIntentStatusSchema,
  payment_method_types: Schema.Array(PaymentMethodTypeSchema),
  settlement_id: Schema.String,
});

export const CreatePaymentIntentRequest = Schema.Struct({
  settlement_id: Schema.String,
  amount: Schema.Number,
  payment_method_type: PaymentMethodTypeSchema,
  currency: Schema.optional(Schema.String),
  metadata: Schema.optional(Schema.Record(Schema.String, Schema.String)),
});

export const ConfirmPaymentIntentRequest = Schema.Struct({
  payment_intent_id: Schema.String,
  payment_method_id: Schema.String,
});

// Type exports
export type PaymentMethodType = Schema.Schema.Type<typeof PaymentMethodTypeSchema>;
export type PaymentIntentStatus = Schema.Schema.Type<typeof PaymentIntentStatusSchema>;
export type PaymentIntent = Schema.Schema.Type<typeof PaymentIntentSchema>;
export type CreatePaymentIntentRequestType = Schema.Schema.Type<typeof CreatePaymentIntentRequest>;
export type ConfirmPaymentIntentRequestType = Schema.Schema.Type<typeof ConfirmPaymentIntentRequest>;

// API Endpoints
export const PaymentsApi = HttpApiGroup.make("payments")
  .add(
    HttpApiEndpoint.post("createPaymentIntent")`/payment-intents`
      .setPayload(CreatePaymentIntentRequest)
      .addSuccess(PaymentIntentSchema)
      .middleware(Authentication)
  )
  .add(
    HttpApiEndpoint.post("confirmPaymentIntent")`/payment-intents/:id/confirm`
      .setPath(Schema.Struct({ id: Schema.String }))
      .setPayload(ConfirmPaymentIntentRequest)
      .addSuccess(PaymentIntentSchema)
      .middleware(Authentication)
  )
  .prefix("/payments");
