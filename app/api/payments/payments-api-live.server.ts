import * as HttpApiBuilder from "@effect/platform/HttpApiBuilder";
import * as Effect from "effect/Effect";
import Strip<PERSON> from "stripe";
import { typeid } from "typeid-js";

import { Api } from "../api";
import { Db } from "../db";
import { CurrentAuth } from "../auth";
import { CloudflareEnv } from "../cloudflare";
import { settlementsTable } from "db/schema";
import { eq } from "drizzle-orm";
import { PaymentIntent } from "./payments-api";

// Create a Stripe client
const getStripeClient = Effect.gen(function* () {
  const env = yield* CloudflareEnv;
  return new Stripe(env.STRIPE_SECRET_KEY, {
    apiVersion: "2024-04-10", // Use the latest API version
  });
});

export const PaymentsApiLive = HttpApiBuilder.group(Api, "payments", (handlers) =>
  handlers
    .handle("createPaymentIntent", (args) =>
      Effect.gen(function* () {
        const auth = yield* CurrentAuth;
        const db = yield* Db;
        const stripe = yield* getStripeClient;

        // Verify the settlement exists and belongs to the user's organization
        const [settlement] = yield* Effect.promise(() =>
          db
            .select()
            .from(settlementsTable)
            .where(eq(settlementsTable.uid, args.payload.settlement_id))
            .limit(1)
        );

        if (!settlement) {
          return yield* Effect.fail(new Error("Settlement not found"));
        }

        if (settlement.organization_id !== auth.orgId) {
          return yield* Effect.fail(new Error("Unauthorized access to settlement"));
        }

        // Create a payment intent
        const paymentIntent = yield* Effect.promise(() =>
          stripe.paymentIntents.create({
            amount: args.payload.amount * 100, // Convert to cents
            currency: args.payload.currency || "usd",
            payment_method_types: [args.payload.payment_method_type],
            metadata: {
              settlement_id: args.payload.settlement_id,
              organization_id: auth.orgId,
              ...args.payload.metadata,
            },
          })
        );

        // Return the payment intent
        return PaymentIntent.make({
          id: paymentIntent.id,
          client_secret: paymentIntent.client_secret!,
          amount: paymentIntent.amount / 100, // Convert back to dollars
          currency: paymentIntent.currency,
          status: paymentIntent.status as any,
          payment_method_types: paymentIntent.payment_method_types as any,
          settlement_id: args.payload.settlement_id,
        });
      })
    )
    .handle("confirmPaymentIntent", (args) =>
      Effect.gen(function* () {
        const auth = yield* CurrentAuth;
        const stripe = yield* getStripeClient;

        // Confirm the payment intent
        const paymentIntent = yield* Effect.promise(() =>
          stripe.paymentIntents.confirm(args.path.id, {
            payment_method: args.payload.payment_method_id,
          })
        );

        // Return the payment intent
        return PaymentIntent.make({
          id: paymentIntent.id,
          client_secret: paymentIntent.client_secret!,
          amount: paymentIntent.amount / 100, // Convert to dollars
          currency: paymentIntent.currency,
          status: paymentIntent.status as any,
          payment_method_types: paymentIntent.payment_method_types as any,
          settlement_id: paymentIntent.metadata?.settlement_id || "",
        });
      })
    )
);
