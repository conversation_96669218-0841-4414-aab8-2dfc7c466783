import { useMutation, useQueryClient } from "@tanstack/react-query";
import * as Effect from "effect/Effect";

import { ApiClient } from "../api-client";
import type { CreatePaymentIntentRequest, ConfirmPaymentIntentRequest } from "./payments-api";

/**
 * Hook to create a payment intent
 */
export const useCreatePaymentIntent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreatePaymentIntentRequest) =>
      Effect.gen(function* () {
        const client = yield* ApiClient;
        return yield* client.payments.createPaymentIntent({
          payload: data,
        });
      }).pipe(Effect.provide(ApiClient.Live), Effect.runPromise),
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ["settlements"] });
    },
  });
};

/**
 * Hook to confirm a payment intent
 */
export const useConfirmPaymentIntent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: ConfirmPaymentIntentRequest }) =>
      Effect.gen(function* () {
        const client = yield* ApiClient;
        return yield* client.payments.confirmPaymentIntent({
          path: { id },
          payload: data,
        });
      }).pipe(Effect.provide(ApiClient.Live), Effect.runPromise),
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ["settlements"] });
    },
  });
};
