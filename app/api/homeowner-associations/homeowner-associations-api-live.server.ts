import * as HttpApiBuilder from "@effect/platform/HttpApiBuilder";
import * as Effect from "effect/Effect";
import { countDistinct, desc, eq, type InferSelectModel } from "drizzle-orm";
import { typeid } from "typeid-js";

import { homeownerAssociationsTable } from "db/schema";
import { Api } from "../api";
import { Db } from "../db";
import { HomeownerAssociation } from "./homeowner-associations-api";
import { DateTime } from "effect";

// Helper function to serialize Homeowner Association row to API response
const serializeHomeownerAssociationRow = (
  hoa: InferSelectModel<typeof homeownerAssociationsTable>,
) => {
  console.log(hoa);
  return HomeownerAssociation.make({
    id: hoa.uid,
    name: hoa.name,
    createdAt: DateTime.unsafeMake(hoa.created_at),
    updatedAt: DateTime.unsafeMake(hoa.updated_at),
  });
};

export const HomeownerAssociationApiLive = HttpApiBuilder.group(
  Api,
  "homeownerAssociations",
  (handlers) =>
    handlers
      // List all Homeowner Associations
      .handle("list", () =>
        Effect.gen(function* () {
          const db = yield* Db;

          const homeownerAssociationRows = yield* Effect.promise(() =>
            db
              .select()
              .from(homeownerAssociationsTable)
              .orderBy(desc(homeownerAssociationsTable.id))
              .limit(100),
          );

          const homeownerAssociationCount = yield* Effect.promise(() =>
            db
              .select({ count: countDistinct(homeownerAssociationsTable.id) })
              .from(homeownerAssociationsTable),
          );

          return {
            items: homeownerAssociationRows.map(
              serializeHomeownerAssociationRow,
            ),
            count: homeownerAssociationCount[0]?.count || 0,
          };
        }).pipe(Effect.tapErrorCause(Effect.logError)),
      )

      // Create a new Homeowner Association
      .handle("create", (args) =>
        Effect.gen(function* () {
          const db = yield* Db;
          const now = yield* DateTime.now;

          const [homeownerAssociationRow] = yield* Effect.promise(() =>
            db
              .insert(homeownerAssociationsTable)
              .values({
                uid: typeid("hoa").toString(),
                name: args.payload.name,
                created_at: DateTime.toDate(now).toISOString(),
                updated_at: DateTime.toDate(now).toISOString(),
              })
              .returning(),
          );

          return serializeHomeownerAssociationRow(homeownerAssociationRow);
        }).pipe(Effect.tapErrorCause(Effect.logError)),
      )

      // Find Homeowner Association by ID
      .handle("findById", (args) =>
        Effect.gen(function* () {
          const db = yield* Db;

          const [homeownerAssociationRow] = yield* Effect.promise(() =>
            db
              .select()
              .from(homeownerAssociationsTable)
              .where(eq(homeownerAssociationsTable.uid, args.path.id)),
          );

          if (!homeownerAssociationRow) {
            throw new Error("Homeowner Association not found");
          }

          return serializeHomeownerAssociationRow(homeownerAssociationRow);
        }).pipe(Effect.tapErrorCause(Effect.logError)),
      )

      // Update Homeowner Association by ID
      .handle("updateById", (args) =>
        Effect.gen(function* () {
          const db = yield* Db;
          const now = new Date().toISOString();

          // First, get the current Homeowner Association to merge with updates
          const [currentHomeownerAssociation] = yield* Effect.promise(() =>
            db
              .select()
              .from(homeownerAssociationsTable)
              .where(eq(homeownerAssociationsTable.uid, args.path.id)),
          );

          if (!currentHomeownerAssociation) {
            throw new Error("Homeowner Association not found");
          }

          // Update the Homeowner Association
          const [updatedHomeownerAssociation] = yield* Effect.promise(() =>
            db
              .update(homeownerAssociationsTable)
              .set({
                name: args.payload.name,
                updated_at: now,
              })
              .where(eq(homeownerAssociationsTable.uid, args.path.id))
              .returning(),
          );

          return serializeHomeownerAssociationRow(updatedHomeownerAssociation);
        }),
      )

      // Delete Homeowner Association by ID
      .handle("deleteById", (args) =>
        Effect.gen(function* () {
          const db = yield* Db;

          // Check if Homeowner Association exists
          const [homeownerAssociationRow] = yield* Effect.promise(() =>
            db
              .select()
              .from(homeownerAssociationsTable)
              .where(eq(homeownerAssociationsTable.uid, args.path.id)),
          );

          if (!homeownerAssociationRow) {
            throw new Error("Homeowner Association not found");
          }

          // Delete the Homeowner Association
          yield* Effect.promise(() =>
            db
              .delete(homeownerAssociationsTable)
              .where(eq(homeownerAssociationsTable.uid, args.path.id)),
          );

          return { success: true };
        }),
      ),
);
