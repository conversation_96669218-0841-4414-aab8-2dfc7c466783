import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import * as Effect from "effect/Effect";

import { ApiClient } from "../api-client";
import type {
  CreateHomeownerAssociation,
  UpdateHomeownerAssociation,
} from "./homeowner-associations-api";

// Hook to fetch all Homeowner Associations
export const useHomeownerAssociations = () => {
  return useQuery({
    queryKey: ["homeownerAssociations"],
    queryFn: () =>
      Effect.gen(function* () {
        const client = yield* ApiClient;
        return yield* client.homeownerAssociations.list();
      }).pipe(Effect.provide(ApiClient.Live), Effect.runPromise),
  });
};

// Hook to fetch a single Homeowner Association by ID
export const useHomeownerAssociation = (id: string) => {
  return useQuery({
    queryKey: ["homeownerAssociations", id],
    queryFn: () =>
      Effect.gen(function* () {
        const client = yield* ApiClient;
        return yield* client.homeownerAssociations.findById({
          path: {
            id: id,
          },
        });
      }).pipe(Effect.provide(ApiClient.Live), Effect.runPromise),
    enabled: !!id,
  });
};

// Hook to create a new Homeowner Association
export const useCreateHomeownerAssociation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateHomeownerAssociation) =>
      Effect.gen(function* () {
        const client = yield* ApiClient;
        return yield* client.homeownerAssociations.create({
          payload: {
            name: data.name,
          },
        });
      }).pipe(Effect.provide(ApiClient.Live), Effect.runPromise),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["homeownerAssociations"] });
    },
  });
};

// Hook to update an existing Homeowner Association
export const useUpdateHomeownerAssociation = (id: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateHomeownerAssociation) =>
      Effect.gen(function* () {
        const client = yield* ApiClient;
        return yield* client.homeownerAssociations.updateById({
          path: {
            id,
          },
          payload: data,
        });
      }).pipe(Effect.provide(ApiClient.Live), Effect.runPromise),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["homeownerAssociations"] });
      queryClient.invalidateQueries({
        queryKey: ["homeownerAssociations", id],
      });
    },
  });
};

// Hook to delete a Homeowner Association
export const useDeleteHomeownerAssociation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) =>
      Effect.gen(function* () {
        const client = yield* ApiClient;
        return yield* client.homeownerAssociations.deleteById({
          path: {
            id,
          },
        });
      }).pipe(Effect.provide(ApiClient.Live), Effect.runPromise),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["homeownerAssociations"] });
    },
  });
};
