import { effectTsResolver } from "@hookform/resolvers/effect-ts";
import { useState } from "react";
import { useForm } from "react-hook-form";

import { Button } from "~/components/ui/button";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";

import { Input } from "~/components/ui/input";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetT<PERSON>le,
  SheetTrigger,
} from "~/components/ui/sheet";

import { CreateHomeownerAssociation } from "../homeowner-associations-api";
import { useCreateHomeownerAssociation } from "../homeowner-associations-hooks";

export const CreateHomeownerAssociationSheet = () => {
  const [open, setOpen] = useState(false);
  const createHomeownerAssociation = useCreateHomeownerAssociation();

  // Initialize the form with react-hook-form and effect-ts resolver
  const form = useForm({
    resolver: effectTsResolver(CreateHomeownerAssociation),
    defaultValues: {
      name: "",
    },
  });

  const handleSubmit = form.handleSubmit((data) => {
    createHomeownerAssociation.mutate(data, {
      onSuccess: () => {
        setOpen(false);
        form.reset();
      },
    });
  });

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button className="flex items-center gap-2">
          Create Homeowner Association
        </Button>
      </SheetTrigger>
      <SheetContent>
        <SheetHeader>
          <SheetTitle>Add New Homeowner Association</SheetTitle>
          <SheetDescription>
            Create a new homeowner association by filling out the form below.
          </SheetDescription>
        </SheetHeader>
        <Form {...form}>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 px-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Homeowner Association name"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <SheetFooter className="pt-4">
              <Button
                type="submit"
                disabled={createHomeownerAssociation.isPending}
              >
                {createHomeownerAssociation.isPending
                  ? "Creating..."
                  : "Create Homeowner Association"}
              </Button>
            </SheetFooter>
          </form>
        </Form>
      </SheetContent>
    </Sheet>
  );
};
