import type { ColumnDef } from "@tanstack/react-table";
import type { HomeownerAssociation } from "~/api/homeowner-associations/homeowner-associations-api";

import { DataTable } from "~/components/data-table";
import { Link } from "react-router";
import { Badge } from "~/components/ui/badge";

const columns: ColumnDef<HomeownerAssociation>[] = [
  {
    accessorKey: "id",
    header: "ID",
    enableHiding: true,
    cell: ({ row }) => <code className="text-xs">{row.getValue("id")}</code>,
  },
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => (
      <div className="font-medium">
        <Link
          to={`/homeowner-associations/${row.getValue("id")}`}
          className="hover:underline flex items-center gap-1"
        >
          {row.getValue("name")}
        </Link>
      </div>
    ),
  },
];

export default function HomeownerAssociationsTable(props: {
  homeownerAssociations: readonly HomeownerAssociation[];
}) {
  return <DataTable data={props.homeownerAssociations} columns={columns} />;
}
