import * as HttpApiEndpoint from "@effect/platform/HttpApiEndpoint";
import * as HttpApiGroup from "@effect/platform/HttpApiGroup";
import * as HttpApiSchema from "@effect/platform/HttpApiSchema";
import * as Schema from "effect/Schema";
import { Authentication } from "../authentication";

export const HomeownerAssociation = Schema.Struct({
  id: Schema.String,
  name: Schema.String,
  createdAt: Schema.DateTimeUtc,
  updatedAt: Schema.DateTimeUtc,
});

export const CreateHomeownerAssociation = Schema.Struct({
  name: Schema.String,
});

export const UpdateHomeownerAssociation = Schema.Struct({
  name: Schema.optional(Schema.String),
});

export type HomeownerAssociation = Schema.Schema.Type<
  typeof HomeownerAssociation
>;
export type CreateHomeownerAssociation = Schema.Schema.Type<
  typeof CreateHomeownerAssociation
>;
export type UpdateHomeownerAssociation = Schema.Schema.Type<
  typeof UpdateHomeownerAssociation
>;

// Define the Homeowner Association API endpoints
export const HomeownerAssociationApi = HttpApiGroup.make(
  "homeownerAssociations",
)
  .add(
    HttpApiEndpoint.get("list")`/`.addSuccess(
      Schema.Struct({
        items: Schema.Array(HomeownerAssociation),
        count: Schema.Number,
      }),
    ),
  )
  .add(
    HttpApiEndpoint.post("create")`/`
      .setPayload(CreateHomeownerAssociation)
      .addSuccess(HomeownerAssociation),
  )
  .add(
    HttpApiEndpoint.get("findById")`/:id`
      .setPath(Schema.Struct({ id: Schema.String }))
      .addSuccess(HomeownerAssociation),
  )
  .add(
    HttpApiEndpoint.patch("updateById")`/:id`
      .setPath(Schema.Struct({ id: Schema.String }))
      .setPayload(UpdateHomeownerAssociation)
      .addSuccess(HomeownerAssociation),
  )
  .add(
    HttpApiEndpoint.del("deleteById")`/:id`
      .setPath(Schema.Struct({ id: Schema.String }))
      .addSuccess(Schema.Struct({ success: Schema.Boolean })),
  )
  .middleware(Authentication)
  .prefix("/homeowner-associations");
