import * as HttpApiClient from "@effect/platform/HttpApiClient";
import * as FetchHttpClient from "@effect/platform/FetchHttpClient";
import * as Context from "effect/Context";
import * as Effect from "effect/Effect";
import * as Layer from "effect/Layer";

import { Api } from "./api";

const make = HttpApiClient.make(Api).pipe(
  Effect.provide(FetchHttpClient.layer),
);

export class ApiClient extends Context.Tag("ApiClient")<
  ApiClient,
  Effect.Effect.Success<typeof make>
>() {
  static readonly Live = Layer.effect(this, make);
}
