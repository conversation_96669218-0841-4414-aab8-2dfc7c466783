import * as HttpApiBuilder from "@effect/platform/HttpApiBuilder";
import * as Effect from "effect/Effect";
import { typeid } from "typeid-js";
import { createClerkClient } from "@clerk/backend";

import { settlementsTable, orgsTable } from "db/schema";
import { Db } from "../db";
import { Api } from "../api";
import { eq, sql } from "drizzle-orm";
import { NotFound } from "../errors";
import { CloudflareEnv } from "../cloudflare";

export const PublicApiLive = HttpApiBuilder.group(Api, "public", (handlers) =>
  handlers
    .handle("submitSettlement", (args) =>
      Effect.gen(function* () {
        const db = yield* Db;
        const now = new Date().toISOString();
        const uid = typeid("settlement").toString();

        try {
          // Log the incoming payload for debugging
          console.log(
            "Settlement submission payload:",
            JSON.stringify(args.payload, null, 2),
          );

          // Validate organization ID
          if (!args.payload.org_id) {
            console.error("Missing organization ID in settlement submission");
            return {
              success: false,
              message: "Organization ID is required",
            };
          }

          // Verify the organization exists
          const [org] = yield* Effect.promise(() =>
            db
              .select()
              .from(orgsTable)
              .where(eq(orgsTable.clerk_id, args.payload.org_id))
              .limit(1),
          );

          if (!org) {
            console.error(
              `Organization not found with ID: ${args.payload.org_id}`,
            );
            return {
              success: false,
              message: "Organization not found",
            };
          }

          // Convert expenses to JSON strings for storage
          const sellerExpenses = JSON.stringify(args.payload.sellerExpenses);
          const buyerExpenses = JSON.stringify(args.payload.buyerExpenses);

          // Insert the settlement into the database
          const [settlementRow] = yield* Effect.promise(() =>
            db
              .insert(settlementsTable)
              .values({
                uid,
                organization_id: args.payload.org_id,
                property_address: args.payload.propertyAddress,
                unit: args.payload.unit || null,
                hoa_name: args.payload.hoaName || null,
                buyer_email: args.payload.buyerEmail || null,
                warranty_deed_key: args.payload.warranty_deed_key || null,
                seller_expenses: sellerExpenses,
                buyer_expenses: buyerExpenses,
                total_amount: args.payload.totalAmount,
                notes: args.payload.notes || null,
                payment_status: "pending",
                created_at: now,
                updated_at: now,
              })
              .returning(),
          );

          console.log("Settlement created successfully:", settlementRow.uid);

          return {
            success: true,
            message: "Settlement submitted successfully",
            settlementId: settlementRow.uid,
          };
        } catch (error) {
          console.error("Error submitting settlement:", error);

          // Provide more detailed error information
          let errorMessage = "Failed to submit settlement";

          if (error instanceof Error) {
            errorMessage += `: ${error.message}`;
            console.error("Error stack:", error.stack);
          }

          return {
            success: false,
            message: errorMessage,
          };
        }
      }).pipe(Effect.tapErrorCause(Effect.logError)),
    )
    .handle("getOrganization", (args) =>
      Effect.gen(function* () {
        const db = yield* Db;

        // Find the organization by ID
        const [org] = yield* Effect.promise(() =>
          db
            .select()
            .from(orgsTable)
            .where(eq(orgsTable.clerk_id, args.path.id))
            .limit(1),
        );

        if (!org) {
          return yield* new NotFound({
            message: "Organization not found",
          });
        }

        return {
          id: org.clerk_id, // Return clerk_id instead of uid
          name: org.name,
          slug: org.slug,
        };
      }).pipe(Effect.tapErrorCause(Effect.logError)),
    )
    .handle("getOrganizationBySlug", (args) =>
      Effect.gen(function* () {
        const db = yield* Db;

        // First, try to find the organization in our database by exact slug match
        const [org] = yield* Effect.promise(() =>
          db
            .select()
            .from(orgsTable)
            .where(eq(orgsTable.slug, args.path.slug))
            .limit(1),
        );

        if (!org) {
          return yield* new NotFound({
            message: "Organization not found",
          });
        }

        return {
          id: org.clerk_id,
          name: org.name,
          slug: org.slug,
        };
      }).pipe(Effect.tapErrorCause(Effect.logError)),
    ),
);
