import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import * as Effect from "effect/Effect";

import { ApiClient } from "../api-client";
import type { PublicSettlementSubmission } from "./public-api";

/**
 * Hook to fetch organization details by ID
 */
export const usePublicOrganization = (id: string, options = {}) => {
  return useQuery({
    queryKey: ["publicOrganization", id],
    queryFn: () =>
      Effect.gen(function* () {
        const client = yield* ApiClient;
        return yield* client.public.getOrganization({
          path: {
            id,
          },
        });
      }).pipe(Effect.provide(ApiClient.Live), Effect.runPromise),
    enabled: !!id,
    retry: 3,
    retryDelay: 1000,
    ...options
  });
};

/**
 * Hook to fetch organization details by slug
 */
export const usePublicOrganizationBySlug = (slug: string, options = {}) => {
  return useQuery({
    queryKey: ["publicOrganizationBySlug", slug],
    queryFn: () =>
      Effect.gen(function* () {
        const client = yield* ApiClient;
        return yield* client.public.getOrganizationBySlug({
          path: {
            slug,
          },
        });
      }).pipe(Effect.provide(ApiClient.Live), Effect.runPromise),
    enabled: !!slug,
    retry: 3,
    retryDelay: 1000,
    ...options
  });
};

/**
 * Hook to submit a settlement from the public form
 */
export const useSubmitPublicSettlement = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: PublicSettlementSubmission) =>
      Effect.gen(function* () {
        const client = yield* ApiClient;
        return yield* client.public.submitSettlement({
          payload: data,
        });
      }).pipe(Effect.provide(ApiClient.Live), Effect.runPromise),
    onSuccess: () => {
      // No need to invalidate queries as this is a public form
      // and we don't have a list of settlements to refresh
    },
  });
};
