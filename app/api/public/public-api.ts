import * as HttpApiEndpoint from "@effect/platform/HttpApiEndpoint";
import * as HttpApiGroup from "@effect/platform/HttpApiGroup";
import * as Schema from "effect/Schema";
import { ExpenseSchema } from "../settlements/settlements-api";
import { NotFound } from "../errors";

// Define the public settlement submission schema
export const PublicSettlementSubmission = Schema.Struct({
  org_id: Schema.String,
  propertyAddress: Schema.String,
  unit: Schema.optional(Schema.String),
  hoaName: Schema.optional(Schema.String),
  buyerEmail: Schema.optional(Schema.String),
  warranty_deed_key: Schema.optional(Schema.String),
  sellerExpenses: Schema.Array(ExpenseSchema),
  buyerExpenses: Schema.Array(ExpenseSchema),
  totalAmount: Schema.Number,
  notes: Schema.optional(Schema.String),
});

export type PublicSettlementSubmission = Schema.Schema.Type<
  typeof PublicSettlementSubmission
>;

// Define the response schema
export const PublicSettlementResponse = Schema.Struct({
  success: Schema.Boolean,
  message: Schema.String,
  settlementId: Schema.optional(Schema.String),
});

export type PublicSettlementResponse = Schema.Schema.Type<
  typeof PublicSettlementResponse
>;

// Define the organization response schema
export const OrganizationResponse = Schema.Struct({
  id: Schema.String,
  name: Schema.String,
  slug: Schema.String,
});

export type OrganizationResponse = Schema.Schema.Type<
  typeof OrganizationResponse
>;

// Create the public API group
export const PublicApi = HttpApiGroup.make("public")
  .add(
    HttpApiEndpoint.post("submitSettlement")`/settlements`
      .setPayload(PublicSettlementSubmission)
      .addSuccess(PublicSettlementResponse),
  )
  .add(
    HttpApiEndpoint.get("getOrganization")`/organizations/:id`
      .setPath(Schema.Struct({ id: Schema.String }))
      .addSuccess(OrganizationResponse)
      .addError(NotFound),
  )
  .add(
    HttpApiEndpoint.get("getOrganizationBySlug")`/organizations/slug/:slug`
      .setPath(Schema.Struct({ slug: Schema.String }))
      .addSuccess(OrganizationResponse)
      .addError(NotFound),
  )
  .prefix("/public");
