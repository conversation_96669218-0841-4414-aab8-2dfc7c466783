import * as HttpApiBuilder from "@effect/platform/HttpApiBuilder";
import * as HttpServer from "@effect/platform/HttpServer";
import * as HttpApiScalar from "@effect/platform/HttpApiScalar";
import * as HttpApiSwagger from "@effect/platform/HttpApiSwagger";
import * as Context from "effect/Context";
import * as Layer from "effect/Layer";

import { Api } from "./api";

import { CloudflareEnv, OriginalRequest } from "./cloudflare";
import { Db, type DbClient } from "./db";

import { HealthApiLive } from "./health/health-api-live.server";
import { SettlementsApiLive } from "./settlements/settlements-api-live.server";
import { HomeownerAssociationApiLive } from "./homeowner-associations/homeowner-associations-api-live.server";
import { PublicApiLive } from "./public/public-api-live.server";
import { PaymentsApiLive } from "./payments/payments-api-live.server";

import { AuthenticationLive } from "./authentication.server";

export const makeHandler = (args: {
  env: Env;
  db: DbClient;
}) => {
  const CloudflareEnvLive = Layer.fresh(Layer.succeed(CloudflareEnv, args.env));
  const DbLive = Layer.fresh(Layer.succeed(Db, args.db));

  const ApiLive = HttpApiBuilder.api(Api).pipe(
    Layer.provide(HealthApiLive),
    Layer.provide(SettlementsApiLive),
    Layer.provide(HomeownerAssociationApiLive),
    Layer.provide(PublicApiLive),
    Layer.provide(PaymentsApiLive),
    Layer.provide(AuthenticationLive),
    Layer.provide(CloudflareEnvLive),
    Layer.provide(DbLive),
  );

  const EnvLive = Layer.empty.pipe(
    Layer.provide(
      HttpApiSwagger.layer({
        path: "/api/docs",
      }),
    ),
    Layer.provide(
      HttpApiScalar.layer({
        path: "/api/scalar",
      }),
    ),
    Layer.provide(
      HttpApiBuilder.middlewareOpenApi({
        path: "/api/openapi.json",
      }),
    ),
    Layer.provideMerge(Layer.mergeAll(ApiLive, HttpServer.layerContext)),
  );

  const { dispose, handler } = HttpApiBuilder.toWebHandler(EnvLive, {
    // middleware: SentryMiddleware(serviceName),
  });

  return handler;
};
