import * as HttpApiBuilder from "@effect/platform/HttpApiBuilder";
import * as Effect from "effect/Effect";

import { Api } from "../api";
import { Db } from "../db";
import { CurrentAuth } from "../auth";

export const HealthApiLive = HttpApiBuilder.group(Api, "health", (handlers) =>
  handlers
    .handle("ping", () =>
      Effect.gen(function* () {
        yield* Effect.log("Ping request received");
        return "Hello, World!";
      }),
    )
    .handle("me", () =>
      Effect.gen(function* () {
        const auth = yield* CurrentAuth;
        return {
          sessionId: auth.sessionId,
          userId: auth.userId,
          orgId: auth.orgId,
        };
      }),
    )
    .handle("size", () =>
      Effect.gen(function* () {
        const db = yield* Db;

        // @ts-expect-error: $client is not a public API
        return db.$client.sql.databaseSize;
      }),
    ),
);
