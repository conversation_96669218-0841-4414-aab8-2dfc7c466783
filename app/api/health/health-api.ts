import * as HttpApiEndpoint from "@effect/platform/HttpApiEndpoint";
import * as HttpApiGroup from "@effect/platform/HttpApiGroup";
import * as HttpApiSchema from "@effect/platform/HttpApiSchema";
import * as Schema from "effect/Schema";
import { Authentication } from "../authentication";

export const HealthApi = HttpApiGroup.make("health")
  .add(HttpApiEndpoint.get("ping")`/`.addSuccess(Schema.String))
  .add(
    HttpApiEndpoint.get("me")`/me`
      .addSuccess(
        Schema.Struct({
          sessionId: Schema.NullishOr(Schema.String),
          userId: Schema.NullishOr(Schema.String),
          orgId: Schema.NullishOr(Schema.String),
        }),
      )
      .middleware(Authentication),
  )
  .add(HttpApiEndpoint.get("size")`/size`.addSuccess(Schema.Number))
  .prefix("/health");
