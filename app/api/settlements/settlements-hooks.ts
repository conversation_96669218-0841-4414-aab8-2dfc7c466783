import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import * as Effect from "effect/Effect";

import { ApiClient } from "../api-client";
import type { CreateSettlement, UpdateSettlement } from "./settlements-api";

// export type Expense = {
//   category: string;
//   amount: number;
// };

// // Helper function to convert API expenses to client expenses
// const convertExpenses = (apiExpenses: unknown): Expense[] | undefined => {
//   if (!apiExpenses) return undefined;
//   if (!Array.isArray(apiExpenses)) return undefined;

//   return apiExpenses.map((exp: Record<string, unknown>) => ({
//     category: typeof exp.category === "string" ? exp.category : "",
//     amount: typeof exp.amount === "number" ? exp.amount : 0,
//   }));
// };

// export type CreateSettlementResponse = Settlement;

export interface SettlementFilters {
  search?: string;
  status?: string;
}

export const useSettlements = (filters?: SettlementFilters) => {
  return useQuery({
    queryKey: ["settlements", filters],
    queryFn: () =>
      Effect.gen(function* () {
        const client = yield* ApiClient;
        return yield* client.settlements.list({
          urlParams: {
            search: filters?.search,
            status: filters?.status,
          },
        });
      }).pipe(Effect.provide(ApiClient.Live), Effect.runPromise),
  });
};

export const useSettlement = (id: string) => {
  return useQuery({
    queryKey: ["settlements", id],
    queryFn: () =>
      Effect.gen(function* () {
        const client = yield* ApiClient;
        return yield* client.settlements.findById({
          path: {
            id,
          },
        });
      }).pipe(Effect.provide(ApiClient.Live), Effect.runPromise),
    enabled: !!id,
  });
};

export const useCreateSettlement = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: CreateSettlement["Type"]) =>
      Effect.gen(function* () {
        const client = yield* ApiClient;
        return yield* client.settlements.create({
          payload,
        });
      }).pipe(Effect.provide(ApiClient.Live), Effect.runPromise),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["settlements"] });
    },
  });
};

// Custom hook for updating a settlement
export const useUpdateSettlement = (id: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: typeof UpdateSettlement.Type) =>
      Effect.gen(function* () {
        const client = yield* ApiClient;
        return yield* client.settlements.updateById({
          path: {
            id,
          },
          payload,
        });
      }).pipe(Effect.provide(ApiClient.Live), Effect.runPromise),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["settlements", id] });
    },
  });
};
