import type { ColumnDef } from "@tanstack/react-table";
import { Calendar, Building, Eye } from "lucide-react";

import { DataTable } from "~/components/data-table";
import { Link } from "react-router";
import type { PaginatedSettlements, Settlement } from "../settlements-api";
import { But<PERSON> } from "~/components/ui/button";
import { SettlementStatusBadge } from "./settlement-status-badge";
import { SettlementCard } from "./settlement-card";
import { useIsMobile } from "~/hooks/use-media-query";

// Helper function to format currency
const formatCurrency = (amount: number | null | undefined) => {
  if (amount === null || amount === undefined) return "--";
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
  }).format(amount);
};

// Helper function to format dates
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  }).format(date);
};

// Create a function to generate columns with the option to hide HOA name
const getColumns = (hideHoaName: boolean): ColumnDef<Settlement["Type"]>[] => [
  {
    accessorKey: "property_address",
    header: () => <div className="property rounded-none !rounded-none">Property</div>,
    minSize: 220,
    maxSize: 350,
    cell: ({ row }) => {
      const address = row.original.property_address;
      const unit = row.original.unit;
      const hoaName = row.original.hoa_name;

      return (
        <div className="flex flex-col">
          <div className="break-words">
            <span className="font-semibold text-gray-800">{address}</span>
            {unit && (
              <span className="text-gray-600 font-medium"> · Unit {unit}</span>
            )}
          </div>
          {!hideHoaName && hoaName && (
            <div className="text-xs text-gray-600 flex items-center gap-1.5 mt-1 break-words">
              <Building className="h-3 w-3 flex-shrink-0 text-gray-500" />
              <span>{hoaName}</span>
            </div>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "payment_status",
    header: () => <div className="status rounded-none !rounded-none">Status</div>,
    minSize: 110,
    maxSize: 140,
    cell: ({ row }) => {
      const status = row.getValue("payment_status") as string;
      return <SettlementStatusBadge status={status} size="sm" />;
    },
  },
  {
    accessorKey: "total_amount",
    header: () => <div className="amount text-right rounded-none !rounded-none">Amount</div>,
    minSize: 120,
    maxSize: 140,
    cell: ({ row }) => {
      const amount = row.getValue("total_amount") as number | undefined;
      return (
        <div className="text-right font-semibold text-gray-800">{formatCurrency(amount)}</div>
      );
    },
  },
  {
    accessorKey: "created_at",
    header: () => <div className="date rounded-none !rounded-none">Date</div>,
    minSize: 130,
    maxSize: 150,
    cell: ({ row }) => {
      const date = row.getValue("created_at") as string;
      return (
        <div className="flex items-center gap-2 text-gray-700">
          <Calendar className="h-3.5 w-3.5 text-gray-500" />
          <span className="font-medium">{formatDate(date)}</span>
        </div>
      );
    },
  },
  {
    id: "actions",
    header: "",
    minSize: 90,
    maxSize: 110,
    cell: ({ row }) => {
      const id = row.original.id;
      return (
        <div className="flex justify-end">
          <Button variant="outline" size="sm" className="h-8 border-gray-200 shadow-sm hover:bg-gray-100 transition-colors rounded-none" asChild>
            <Link to={`/settlements/${id}`} className="flex items-center gap-2">
              <Eye className="h-3.5 w-3.5 text-gray-600" />
              <span className="font-medium">View</span>
            </Link>
          </Button>
        </div>
      );
    },
  },
];

export default function SettlementsTable(props: {
  settlements: (typeof PaginatedSettlements)["Type"];
  hideHoaName?: boolean;
}) {
  const { hideHoaName = false } = props;
  const isMobile = useIsMobile();

  // Use card view for mobile devices
  if (isMobile) {
    return (
      <div className="space-y-4">
        {props.settlements.items.length === 0 ? (
          <div className="border border-gray-200 bg-background shadow-sm overflow-hidden">
            <div className="flex flex-col items-center justify-center py-16">
              <div className="rounded-full bg-gray-100 p-4 mb-5 shadow-sm">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-7 w-7 text-gray-600">
                  <title>Chat bubble icon</title>
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                </svg>
              </div>
              <p className="text-xl font-medium text-gray-900 mb-3">No results found</p>
              <p className="text-sm text-gray-500 max-w-md mx-auto px-4 text-center mb-6">
                No settlements match your current search criteria. Try adjusting your filters or create a new settlement.
              </p>
              <button
                type="button"
                onClick={() => (document.querySelector('[aria-label="Initiate Settlement"]') as HTMLAnchorElement)?.click()}
                className="inline-flex items-center justify-center gap-2 px-4 py-2 bg-sky-600 hover:bg-sky-700 text-white shadow-sm transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <title>Icon</title>
                  <path d="M12 5v14M5 12h14" />
                </svg>
                <span>Initiate Settlement</span>
              </button>
            </div>
          </div>
        ) : (
          props.settlements.items.map((settlement) => (
            <SettlementCard
              key={settlement.id}
              settlement={settlement}
              hideHoaName={hideHoaName}
            />
          ))
        )}
      </div>
    );
  }

  // Use table view for desktop
  return (
    <DataTable
      data={props.settlements.items}
      columns={getColumns(hideHoaName)}
    />
  );
}
