import { useMemo } from "react";
import { Building } from "lucide-react";
import type { PaginatedSettlements, Settlement } from "../settlements-api";
import SettlementsTable from "./settlements-table";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { useIsMobile } from "~/hooks/use-media-query";

// Define the grouped settlements type
interface GroupedSettlements {
  [key: string]: Settlement["Type"][];
}

export default function GroupedSettlements(props: {
  settlements: (typeof PaginatedSettlements)["Type"];
}) {
  const isMobile = useIsMobile();
  // Group settlements by HOA name
  const groupedSettlements = useMemo(() => {
    const grouped = props.settlements.items.reduce((acc, settlement) => {
      if (settlement.hoa_name) {
        if (!acc[settlement.hoa_name]) {
          acc[settlement.hoa_name] = [];
        }
        acc[settlement.hoa_name].push(settlement);
      }
      return acc;
    }, {} as GroupedSettlements);

    // Then, add all settlements without HOA names to a "No HOA" group
    const noHoaSettlements = props.settlements.items.filter(
      (settlement) => !settlement.hoa_name
    );

    if (noHoaSettlements.length > 0) {
      grouped["No HOA"] = noHoaSettlements;
    }

    return grouped;
  }, [props.settlements.items]);

  // Get sorted HOA names
  const sortedHoaNames = useMemo(() => {
    return Object.keys(groupedSettlements).sort((a, b) => {
      // Put "No HOA" at the end
      if (a === "No HOA") return 1;
      if (b === "No HOA") return -1;
      return a.localeCompare(b);
    });
  }, [groupedSettlements]);

  // If there are no settlements, show empty state
  if (props.settlements.items.length === 0) {
    return (
      <div className="rounded-lg border border-gray-200 bg-background shadow-sm overflow-hidden">
        <div className="flex flex-col items-center justify-center py-12 sm:py-16 px-4 sm:px-6">
          <div className="rounded-full bg-gray-100 p-4 mb-5 shadow-sm">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-7 w-7 text-gray-600">
              <title>Chat bubble icon</title>
              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
            </svg>
          </div>
          <p className="text-xl font-medium text-gray-900 mb-3">No settlements found</p>
          <p className="text-sm text-gray-500 max-w-md mx-auto text-center mb-6">
            {props.settlements.items.length === 0 && window.location.search ? (
              <>No settlements match your current search criteria. Try adjusting your filters.</>
            ) : (
              <>You haven't created any settlements yet. Get started by initiating your first settlement.</>
            )}
          </p>
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
            <button
              type="button"
              onClick={() => (document.querySelector('[aria-label="Initiate Settlement"]') as HTMLAnchorElement).click()}
              className="inline-flex items-center justify-center gap-2 px-4 py-2 bg-sky-600 hover:bg-sky-700 text-white rounded-lg shadow-sm transition-colors"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <title>Icon</title>
                <path d="M12 5v14M5 12h14" />
              </svg>
              <span>Initiate Settlement</span>
            </button>
            {props.settlements.items.length === 0 && window.location.search && (
              <button
                type="button"
                onClick={() => window.location.replace(window.location.pathname)}
                className="inline-flex items-center justify-center gap-2 px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-lg shadow-sm hover:bg-gray-50 transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <title>Icon</title>
                  <path d="M3 3v18h18" />
                  <path d="m3 17 8-8 4 4 8-8" />
                </svg>
                <span>Clear Filters</span>
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  // If there's only one group and it's "No HOA", just show the regular table
  if (sortedHoaNames.length === 1 && sortedHoaNames[0] === "No HOA") {
    return <SettlementsTable settlements={props.settlements} />;
  }

  return (
    <div className={isMobile ? "space-y-6" : "space-y-8"}>
      {sortedHoaNames.map((hoaName) => (
        <Card key={hoaName} className="border border-gray-200 shadow-md overflow-hidden py-0 gap-0 rounded-none">
          <CardHeader
            className={`${isMobile ? "py-2.5 px-4" : "py-3 px-5"} border-b border-gray-200 gap-0 rounded-none ${hoaName === "No HOA"
              ? "bg-gradient-to-r from-gray-50/90 to-white"
              : "bg-gradient-to-r from-blue-50/80 to-white"
              }`}>
            <CardTitle className={`${isMobile ? "text-base" : "text-lg"} flex items-center gap-2 flex-wrap m-0 py-0`}>
              {hoaName === "No HOA" ? (
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5 text-gray-500 flex-shrink-0">
                  <title>Icon</title>
                  <path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z" />
                  <path d="M3 9V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v4" />
                  <path d="M12 12v5" />
                </svg>
              ) : (
                <Building className="h-5 w-5 text-gray-600 flex-shrink-0" />
              )}
              <span className="font-semibold text-gray-800">{hoaName}</span>
              <span className="text-sm font-normal text-muted-foreground ml-1">
                ({groupedSettlements[hoaName].length} {groupedSettlements[hoaName].length === 1 ? "settlement" : "settlements"})
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0 pt-0">
            <SettlementsTable
              settlements={{
                items: groupedSettlements[hoaName],
                count: groupedSettlements[hoaName].length,
              }}
              hideHoaName={true}
            />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
