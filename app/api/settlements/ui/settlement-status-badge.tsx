import { Badge } from "~/components/ui/badge";
import { cn } from "~/lib/utils";

export type SettlementStatus =
  | "pending"
  | "paid"
  | "overdue"
  | "cancelled"
  | string;

interface SettlementStatusBadgeProps {
  status: SettlementStatus;
  className?: string;
  size?: "default" | "sm" | "lg";
}

/**
 * Get the appropriate variant for a status badge based on the status value
 */
export function getStatusVariant(status: SettlementStatus) {
  switch (status.toLowerCase()) {
    case "pending":
      return "warning" as const;
    case "paid":
      return "success" as const;
    case "overdue":
      return "destructive" as const;
    case "cancelled":
      return "outline" as const;
    default:
      return "default" as const;
  }
}

/**
 * A component for displaying settlement status with appropriate styling
 */
export function SettlementStatusBadge({
  status,
  className,
  size = "default",
}: SettlementStatusBadgeProps) {
  const variant = getStatusVariant(status);

  return (
    <Badge
      variant={variant}
      className={cn(
        "capitalize",
        size === "sm" && "text-xs py-0 px-2",
        size === "lg" && "text-sm py-1 px-3",
        className,
      )}
    >
      {status}
    </Badge>
  );
}
