import { ResourceId } from "~/components/resource-id";
import { SettlementStatusBadge } from "./settlement-status-badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import {
  Calendar,
  Clock,
  DollarSign,
} from "lucide-react";
import type { Settlement } from "../settlements-api";

// Helper function to format dates
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  }).format(date);
};

// Helper function to format currency
const formatCurrency = (amount: number | null | undefined) => {
  if (amount === null || amount === undefined) return "--";
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
  }).format(amount);
};

interface SettlementHeaderProps {
  settlement: Settlement["Type"];
}

export function SettlementHeader({ settlement }: SettlementHeaderProps) {
  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-2xl">
              {settlement.name ||
                `Property at ${settlement.property_address}`}
            </CardTitle>
            <CardDescription className="mt-1 flex items-center gap-1">
              Settlement ID:{" "}
              <ResourceId id={settlement.id} truncate={false} />
            </CardDescription>
          </div>
          <SettlementStatusBadge
            status={settlement.payment_status}
            size="lg"
          />
        </div>
      </CardHeader>
      <CardContent className="pt-4">
        <div className="flex flex-wrap gap-x-8 gap-y-3 text-sm">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-gray-500" />
            <span className="text-muted-foreground">Created {formatDate(settlement.created_at)}</span>
          </div>
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-gray-500" />
            <span className="text-muted-foreground">Updated {formatDate(settlement.updated_at)}</span>
          </div>
          {settlement.total_amount && (
            <div className="flex items-center gap-2 font-medium">
              <DollarSign className="h-4 w-4 text-gray-500" />
              <span>{formatCurrency(settlement.total_amount)}</span>
            </div>
          )}
          {/* Unit information */}
          {settlement.unit && (
            <div className="flex items-center gap-2 ml-auto">
              <span className="text-xs text-muted-foreground">Unit:</span>
              <span className="text-sm">{settlement.unit}</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
