import { Building, Calendar, Eye } from "lucide-react";
import { <PERSON> } from "react-router";
import { Button } from "~/components/ui/button";
import type { Settlement } from "../settlements-api";
import { SettlementStatusBadge } from "./settlement-status-badge";

// Helper function to format currency
const formatCurrency = (amount: number | null | undefined) => {
  if (amount === null || amount === undefined) return "--";
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
  }).format(amount);
};

// Helper function to format dates
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  }).format(date);
};

interface SettlementCardProps {
  settlement: Settlement["Type"];
  hideHoaName?: boolean;
}

export function SettlementCard({ settlement, hideHoaName = false }: SettlementCardProps) {
  return (
    <div className="border border-gray-200 bg-white shadow-sm p-4 mb-4 hover:shadow-md transition-shadow">
      <div className="flex flex-col space-y-3">
        {/* Property Address */}
        <div className="break-words">
          <h3 className="font-medium text-gray-800 text-base">{settlement.property_address}</h3>
          {settlement.unit && (
            <span className="text-muted-foreground text-sm"> · Unit {settlement.unit}</span>
          )}
        </div>

        {/* HOA Name */}
        {!hideHoaName && settlement.hoa_name && (
          <div className="text-xs text-muted-foreground flex items-center gap-1 mt-0.5 break-words">
            <Building className="h-3.5 w-3.5 flex-shrink-0 text-gray-500" />
            <span>{settlement.hoa_name}</span>
          </div>
        )}

        {/* Divider */}
        <div className="border-t border-gray-100 mt-1.5 mb-1"></div>

        {/* Status and Amount */}
        <div className="flex justify-between items-center">
          <div>
            <div className="text-xs text-muted-foreground mb-1.5 font-medium">Status</div>
            <SettlementStatusBadge status={settlement.payment_status} size="sm" />
          </div>
          <div className="text-right">
            <div className="text-xs text-muted-foreground mb-1.5 font-medium">Amount</div>
            <div className="font-medium text-gray-800">{formatCurrency(settlement.total_amount)}</div>
          </div>
        </div>

        {/* Date */}
        <div className="flex items-center gap-1.5 text-muted-foreground text-sm mt-1">
          <Calendar className="h-3.5 w-3.5 text-gray-500" />
          <span>{formatDate(settlement.created_at)}</span>
        </div>

        {/* View Button */}
        <Button
          variant="outline"
          size="sm"
          className="w-full mt-3 border-gray-200 shadow-sm hover:bg-gray-50 hover:border-gray-300 transition-colors"
          asChild
        >
          <Link to={`/settlements/${settlement.id}`} className="flex items-center justify-center gap-1.5">
            <Eye className="h-4 w-4 text-gray-600" />
            <span>View Details</span>
          </Link>
        </Button>
      </div>
    </div>
  );
}
