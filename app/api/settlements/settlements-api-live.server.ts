import * as HttpApiBuilder from "@effect/platform/HttpApiBuilder";
import * as Effect from "effect/Effect";

import {
  and,
  countDistinct,
  desc,
  eq,
  like,
  or,
  type InferSelectModel,
} from "drizzle-orm";
import { typeid } from "typeid-js";

import { settlementsTable } from "db/schema";
import { Api } from "../api";
import { Db } from "../db";
import { Settlement } from "./settlements-api";
import { CurrentAuth } from "../auth";

const serializeSettlementRow = (
  settlement: InferSelectModel<typeof settlementsTable>,
) => {
  // Parse JSON strings for expenses if they exist
  const sellerExpenses = settlement.seller_expenses
    ? JSON.parse(settlement.seller_expenses as string)
    : undefined;

  const buyerExpenses = settlement.buyer_expenses
    ? JSON.parse(settlement.buyer_expenses as string)
    : undefined;

  // No need to convert isTest anymore as it's been removed

  return Settlement.make({
    id: settlement.uid,
    name: settlement.name || undefined,
    organization_id: settlement.organization_id,
    property_address: settlement.property_address,
    unit: settlement.unit || undefined,
    hoa_name: settlement.hoa_name || undefined,
    buyer_email: settlement.buyer_email || undefined,
    warranty_deed_key: settlement.warranty_deed_key || undefined,
    amount: settlement.amount !== null ? settlement.amount : undefined,
    payment_status: settlement.payment_status,
    seller_expenses: sellerExpenses,
    buyer_expenses: buyerExpenses,
    total_amount:
      settlement.total_amount !== null ? settlement.total_amount : undefined,
    notes: settlement.notes || undefined,
    created_at: settlement.created_at,
    updated_at: settlement.updated_at,
  });
};

export const SettlementsApiLive = HttpApiBuilder.group(
  Api,
  "settlements",
  (handlers) =>
    handlers
      .handle("list", (args) =>
        Effect.gen(function* () {
          const db = yield* Db;
          const auth = yield* CurrentAuth;

          // Extract search and status filters from URL parameters
          const search = args.urlParams?.search;
          const status = args.urlParams?.status;

          // Prepare search term if search is provided
          const searchTerm =
            search && search.trim() !== "" ? `%${search.trim()}%` : undefined;

          // Prepare status filter if status is provided
          const statusFilter =
            status && status.trim() !== "" ? status.trim() : undefined;

          // Build query with conditional filters
          const query = db
            .select()
            .from(settlementsTable)
            .where(
              and(
                // Always filter by organization_id
                eq(settlementsTable.organization_id, auth.orgId),

                // Conditionally add search filter
                searchTerm
                  ? or(
                      like(settlementsTable.property_address, searchTerm),
                      like(settlementsTable.unit || "", searchTerm),
                      like(settlementsTable.buyer_email || "", searchTerm),
                    )
                  : undefined,

                // Conditionally add status filter
                statusFilter
                  ? eq(settlementsTable.payment_status, statusFilter)
                  : undefined,
              ),
            );

          // Execute the query with ordering and limit
          const settlementRows = yield* Effect.promise(() =>
            query.orderBy(desc(settlementsTable.id)).limit(100),
          );

          // Count total settlements with the same filters
          const settlementsCount = yield* Effect.promise(() =>
            db
              .select({ count: countDistinct(settlementsTable.id) })
              .from(settlementsTable)
              .where(
                and(
                  // Always filter by organization_id
                  eq(settlementsTable.organization_id, auth.orgId),

                  // Conditionally add search filter
                  searchTerm
                    ? or(
                        like(settlementsTable.property_address, searchTerm),
                        like(settlementsTable.unit || "", searchTerm),
                        like(settlementsTable.buyer_email || "", searchTerm),
                      )
                    : undefined,

                  // Conditionally add status filter
                  statusFilter
                    ? eq(settlementsTable.payment_status, statusFilter)
                    : undefined,
                ),
              ),
          );

          return {
            items: settlementRows.map(serializeSettlementRow),
            count: settlementsCount[0]?.count || 0,
          };
        }).pipe(Effect.tapErrorCause(Effect.logError)),
      )
      .handle("create", (args) =>
        Effect.gen(function* () {
          const db = yield* Db;
          const now = new Date().toISOString();
          const uid = typeid("settlement").toString();

          const auth = yield* CurrentAuth;

          console.log(auth);

          // Convert expenses arrays to JSON strings if they exist
          const sellerExpenses = args.payload.seller_expenses
            ? JSON.stringify(args.payload.seller_expenses)
            : null;

          const buyerExpenses = args.payload.buyer_expenses
            ? JSON.stringify(args.payload.buyer_expenses)
            : null;

          // No need to convert isTest anymore as it's been removed

          const [settlementRow] = yield* Effect.promise(() =>
            db
              .insert(settlementsTable)
              .values({
                uid,
                name: args.payload.name || null,
                organization_id: auth.orgId,
                property_address: args.payload.property_address,
                unit: args.payload.unit || null,
                hoa_name: args.payload.hoa_name || null,
                buyer_email: args.payload.buyer_email || null,
                warranty_deed_key: args.payload.warranty_deed_key || null,
                amount: args.payload.amount || null,
                payment_status: "pending",
                seller_expenses: sellerExpenses,
                buyer_expenses: buyerExpenses,
                total_amount: args.payload.total_amount || null,
                notes: args.payload.notes || null,
                created_at: now,
                updated_at: now,
              })
              .returning(),
          );

          return serializeSettlementRow(settlementRow);
        }).pipe(Effect.tapErrorCause(Effect.logError)),
      )
      .handle("findById", (args) =>
        Effect.gen(function* () {
          const db = yield* Db;
          const auth = yield* CurrentAuth;

          const [settlementRow] = yield* Effect.promise(() =>
            db
              .select()
              .from(settlementsTable)
              .where((t) =>
                and(eq(t.organization_id, auth.orgId), eq(t.uid, args.path.id)),
              ),
          );

          if (!settlementRow) {
            throw new Error("Settlement not found");
          }

          return serializeSettlementRow(settlementRow);
        }).pipe(Effect.tapErrorCause(Effect.logError)),
      )
      .handle("updateById", (args) =>
        Effect.gen(function* () {
          const db = yield* Db;
          const auth = yield* CurrentAuth;
          const now = new Date().toISOString();

          // First, get the current settlement to verify it exists and belongs to the user's organization
          const [currentSettlement] = yield* Effect.promise(() =>
            db
              .select()
              .from(settlementsTable)
              .where((t) =>
                and(eq(t.organization_id, auth.orgId), eq(t.uid, args.path.id)),
              ),
          );

          if (!currentSettlement) {
            throw new Error("Settlement not found");
          }

          // Update the settlement with the provided fields
          const [updatedSettlement] = yield* Effect.promise(() =>
            db
              .update(settlementsTable)
              .set({
                property_address: args.payload.property_address || currentSettlement.property_address,
                unit: args.payload.unit !== undefined ? args.payload.unit : currentSettlement.unit,
                hoa_name: args.payload.hoa_name !== undefined ? args.payload.hoa_name : currentSettlement.hoa_name,
                buyer_email: args.payload.buyer_email !== undefined ? args.payload.buyer_email : currentSettlement.buyer_email,
                updated_at: now,
              })
              .where(eq(settlementsTable.uid, args.path.id))
              .returning(),
          );

          return serializeSettlementRow(updatedSettlement);
        }).pipe(Effect.tapErrorCause(Effect.logError)),
      ),
);
