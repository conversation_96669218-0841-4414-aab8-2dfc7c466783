// Expense category constants
export const EXPENSE_CATEGORIES = {
  DUES: "dues",
  SPECIAL_ASSESSMENT: "special-assessment",
  MOVE_IN_OUT_FEE: "move-in-out-fee",
  TRANSFER_FEE: "transfer-fee",
  WORKING_CAPITAL: "working-capital",
  OTHER: "other",
} as const;

// Type for expense categories
export type ExpenseCategory = typeof EXPENSE_CATEGORIES[keyof typeof EXPENSE_CATEGORIES];

// Helper function to get human-readable category names
export function getCategoryLabel(category: ExpenseCategory): string {
  switch (category) {
    case EXPENSE_CATEGORIES.DUES:
      return "Dues";
    case EXPENSE_CATEGORIES.SPECIAL_ASSESSMENT:
      return "Special Assessment";
    case EXPENSE_CATEGORIES.MOVE_IN_OUT_FEE:
      return "Move In/Out Fee";
    case EXPENSE_CATEGORIES.TRANSFER_FEE:
      return "Transfer Fee";
    case EXPENSE_CATEGORIES.WORKING_CAPITAL:
      return "Working Capital";
    case EXPENSE_CATEGORIES.OTHER:
      return "Other";
    default:
      return category;
  }
}

// Array of all expense categories for easy iteration
export const ALL_EXPENSE_CATEGORIES = Object.values(EXPENSE_CATEGORIES);

// Months for dues selection
export const MONTHS = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

// Generate years for selection (current year and next 5 years)
const currentYear = new Date().getFullYear();
export const YEARS = Array.from({ length: 6 }, (_, i) => (currentYear + i).toString());
