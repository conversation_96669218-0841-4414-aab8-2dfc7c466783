import * as HttpApiEndpoint from "@effect/platform/HttpApiEndpoint";
import * as HttpApiGroup from "@effect/platform/HttpApiGroup";
import * as Schema from "effect/Schema";
import { Authentication } from "../authentication";
import { EXPENSE_CATEGORIES } from "./expense-categories";

const ExpenseCategory = Schema.Literal(
  "dues",
  "special-assessment",
  "move-in-out-fee",
  "transfer-fee",
  "working-capital",
  "other",
);

// Define the expense schema for buyer and seller expenses
export const ExpenseSchema = Schema.Struct({
  category: Schema.Union(ExpenseCategory, Schema.String),
  amount: Schema.Number,
  // Optional subcategory fields
  subcategory: Schema.optional(Schema.String), // For 'other' category
  month: Schema.optional(Schema.String), // For 'dues' category
  year: Schema.optional(Schema.String), // For 'dues' category
});

export const Settlement = Schema.Struct({
  id: Schema.String,
  name: Schema.optional(Schema.String),
  organization_id: Schema.String,
  property_address: Schema.String,
  unit: Schema.optional(Schema.String),
  hoa_name: Schema.optional(Schema.String),
  buyer_email: Schema.optional(Schema.String),
  warranty_deed_key: Schema.optional(Schema.String),
  amount: Schema.optional(Schema.Number),
  payment_status: Schema.String,
  seller_expenses: Schema.optional(Schema.Array(ExpenseSchema)),
  buyer_expenses: Schema.optional(Schema.Array(ExpenseSchema)),
  total_amount: Schema.optional(Schema.Number),
  notes: Schema.optional(Schema.String),
  created_at: Schema.String,
  updated_at: Schema.String,
});

export type Settlement = typeof Settlement;

export const CreateSettlement = Schema.Struct({
  name: Schema.optional(Schema.String),
  property_address: Schema.String,
  unit: Schema.optional(Schema.String),
  hoa_name: Schema.optional(Schema.String),
  buyer_email: Schema.optional(Schema.String),
  warranty_deed_key: Schema.optional(Schema.String),
  amount: Schema.optional(Schema.Number),
  seller_expenses: Schema.optional(Schema.Array(ExpenseSchema)),
  buyer_expenses: Schema.optional(Schema.Array(ExpenseSchema)),
  total_amount: Schema.optional(Schema.Number),
  notes: Schema.optional(Schema.String),
});

export type CreateSettlement = typeof CreateSettlement;

export const UpdateSettlement = Schema.Struct({
  property_address: Schema.optional(Schema.String),
  unit: Schema.optional(Schema.String),
  hoa_name: Schema.optional(Schema.String),
  buyer_email: Schema.optional(Schema.String),
});

export type UpdateSettlement = typeof UpdateSettlement;

export const PaginatedSettlements = Schema.Struct({
  items: Schema.Array(Settlement),
  count: Schema.Number,
});

export const SettlementsApi = HttpApiGroup.make("settlements")
  .add(
    HttpApiEndpoint.get("list")`/`
      .setUrlParams(
        Schema.Struct({
          search: Schema.optional(Schema.String),
          status: Schema.optional(Schema.String),
        }),
      )
      .addSuccess(PaginatedSettlements),
  )
  .add(
    HttpApiEndpoint.post("create")`/`
      .setPayload(CreateSettlement)
      .addSuccess(Settlement),
  )
  .add(
    HttpApiEndpoint.get("findById")`/:id`
      .setPath(Schema.Struct({ id: Schema.String }))
      .addSuccess(Settlement),
  )
  .add(
    HttpApiEndpoint.patch("updateById")`/:id`
      .setPath(Schema.Struct({ id: Schema.String }))
      .setPayload(UpdateSettlement)
      .addSuccess(Settlement),
  )
  .middleware(Authentication)
  .prefix("/settlements");
