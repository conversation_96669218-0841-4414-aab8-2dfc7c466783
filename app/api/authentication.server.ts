import { Effect, Layer } from "effect";
import { Authentication, Unauthorized } from "./authentication";
import { CurrentAuth } from "./auth";
import { createClerkClient } from "@clerk/backend";
import { CloudflareEnv, OriginalRequest } from "./cloudflare";
import { getAuth } from "@clerk/react-router/ssr.server";
import type { LoaderFunctionArgs } from "react-router";
import { Db } from "./db";
import { usersTable, orgsTable } from "../../db/schema";
import { eq } from "drizzle-orm";
import { typeid } from "typeid-js";

export const AuthenticationLive = Layer.effect(
  Authentication,
  Effect.gen(function* () {
    const env = yield* CloudflareEnv;

    return Authentication.of({
      session: (_token) =>
        Effect.gen(function* () {
          const optionalDb = yield* Effect.serviceOption(Db);
          const db = yield* optionalDb.pipe(
            Effect.catchTag(
              "NoSuchElementException",
              () => new Unauthorized({ message: "Unauthorized: No database" }),
            ),
          );

          const optionalRequest = yield* Effect.serviceOption(OriginalRequest);
          const request = yield* optionalRequest.pipe(
            Effect.catchTag(
              "NoSuchElementException",
              () => new Unauthorized({ message: "Unauthorized: No request" }),
            ),
          );

          const args = {
            request,
            context: {},
            params: {},
          } as LoaderFunctionArgs;

          const auth = yield* Effect.promise(() =>
            getAuth(args, {
              // @ts-expect-error
              publishableKey: env.VITE_CLERK_PUBLISHABLE_KEY,
              secretKey: env.CLERK_SECRET_KEY,
            }),
          );

          if (!auth.sessionId) {
            return yield* new Unauthorized({
              message: "Unauthorized: No session",
            });
          }
          if (!auth.userId) {
            return yield* new Unauthorized({
              message: "Unauthorized: No user",
            });
          }
          if (!auth.orgId) {
            return yield* new Unauthorized({
              message: "Unauthorized: No org",
            });
          }

          // Create Clerk client to fetch detailed user and org info
          const clerk = createClerkClient({
            secretKey: env.CLERK_SECRET_KEY,
          });

          const existingUsers = yield* Effect.promise(() =>
            db
              .select()
              .from(usersTable)
              .where(eq(usersTable.clerk_id, auth.userId))
              .limit(1),
          );

          if (existingUsers.length === 0) {
            const clerkUser = yield* Effect.promise(() =>
              clerk.users.getUser(auth.userId),
            );

            yield* Effect.promise(() =>
              db.insert(usersTable).values({
                uid: typeid("user").toString(),
                clerk_id: auth.userId,
                name:
                  clerkUser.firstName && clerkUser.lastName
                    ? `${clerkUser.firstName} ${clerkUser.lastName}`
                    : clerkUser.username || "Unknown",
                email: clerkUser.emailAddresses[0]?.emailAddress || "",
              }),
            );
          }

          // Fetch the latest organization data from Clerk
          const clerkOrg = yield* Effect.promise(() =>
            clerk.organizations.getOrganization({
              organizationId: auth.orgId as string,
            }),
          );

          const [org] = yield* Effect.promise(() =>
            db
              .select()
              .from(orgsTable)
              .where(eq(orgsTable.clerk_id, auth.orgId as string))
              .limit(1),
          );

          if (!org) {
            yield* Effect.promise(() =>
              db.insert(orgsTable).values({
                uid: typeid("org").toString(),
                clerk_id: auth.orgId as string,
                name: clerkOrg.name || "Unknown Organization",
                slug: clerkOrg.slug || "unknown",
              }),
            );
          }

          if (org.name !== clerkOrg.name || org.slug !== clerkOrg.slug) {
            yield* Effect.promise(() =>
              db
                .update(orgsTable)
                .set({
                  name: clerkOrg.name,
                  slug: clerkOrg.slug || slugify(clerkOrg.name),
                })
                .where(eq(orgsTable.clerk_id, auth.orgId as string)),
            );
          }

          return CurrentAuth.of({
            sessionId: auth.sessionId,
            userId: auth.userId,
            orgId: auth.orgId,
          });
        }).pipe(Effect.tapErrorCause(Effect.logError)),
    });
  }),
);

function slugify(str: string) {
  return str
    .replace(/^\s+|\s+$/g, "")
    .toLowerCase()
    .replace(/[^a-z0-9 -]/g, "") // remove any non-alphanumeric characters
    .replace(/\s+/g, "-") // replace spaces with hyphens
    .replace(/-+/g, "-"); // remove consecutive hyphens
}
