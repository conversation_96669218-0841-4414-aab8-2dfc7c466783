import * as HttpApi from "@effect/platform/HttpApi";

import { HealthApi } from "./health/health-api";
import { SettlementsApi } from "./settlements/settlements-api";
import { HomeownerAssociationApi } from "./homeowner-associations/homeowner-associations-api";
import { PublicApi } from "./public/public-api";
// import { PaymentsApi } from "./payments/payments-api";

// Define our API with groups for each resource/feature area
export const Api = HttpApi.make("Api")
  .add(HealthApi)
  .add(SettlementsApi)
  .add(HomeownerAssociationApi)
  .add(PublicApi)
  // .add(PaymentsApi)
  .prefix("/api");
