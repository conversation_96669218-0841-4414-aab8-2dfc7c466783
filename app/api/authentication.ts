import {
  HttpApiMiddleware,
  HttpApiSchema,
  HttpApiSecurity,
} from "@effect/platform";
import { CurrentAuth } from "./auth";
import { Schema } from "effect";

export class Unauthorized extends Schema.TaggedError<Unauthorized>()(
  "Unauthorized",
  {
    message: Schema.String,
  },
  HttpApiSchema.annotations({ status: 401 }),
) {}

export const userSecurity = HttpApiSecurity.apiKey({
  key: "__session",
  in: "cookie",
});

export class Authentication extends HttpApiMiddleware.Tag<Authentication>()(
  "Authentication",
  {
    // Add your error schema
    failure: Unauthorized,
    // Add the Context.Tag that the middleware will provide
    provides: CurrentAuth,
    security: {
      session: userSecurity,
    },
  },
) {}
