# TitleSync

## 🏗 Tech Stack

### **Frontend**

- [Vite](https://vitejs.dev/) – Fast and modern build tool
- [React](https://react.dev/) – UI framework for composable interfaces
- [Shadcn](https://ui.shadcn.com/) – Elegant, accessible UI components
- [Tailwind CSS](https://tailwindcss.com/) – Utility-first styling
- [React Router](https://reactrouter.com/) – Declarative navigation
- [TanStack React Query](https://tanstack.com/query) – Async state management
- [TanStack Table](https://tanstack.com/table) – High-performance tables
- [React Hook Form](https://react-hook-form.com/) – Flexible form management

### **Backend**

- [Effect](https://effect.website/) – Functional programming framework for TypeScript
- [Cloudflare Workers](https://workers.cloudflare.com/) – Serverless edge execution

### **Database**

- [SQLite](https://sqlite.org/) – Lightweight, embedded SQL database
- [Drizzle ORM](https://orm.drizzle.team/) – Type-safe SQL ORM for TypeScript

### **Hosting**

- **Cloudflare Workers** (current) – Serverless, globally distributed
- **Future support** for any host via portable architecture

## 🛠️ Setup & Development

### Prerequisites

- [Node.js](https://nodejs.org/) (LTS recommended)
- [pnpm](https://pnpm.io/) (recommended) or npm/yarn
- Cloudflare account (for Workers deployment)

### Repository Structure

The repository is organized as follows:
- `/app` - React application code
- `/db` - Database schema and migrations
- `/workers` - Cloudflare Workers code
- `/public` - Static assets

### Installation

```sh
pnpm install  # or npm install / yarn install
```

### Running the Dev Server

```sh
pnpm dev  # Runs Vite dev server
```

The development server will be available at http://localhost:1234.

### Deploying to Cloudflare Workers

```sh
pnpm run deploy  # Uses Cloudflare Wrangler
```

### Troubleshooting

#### React Router Issues

This project uses React Router v7. If you encounter errors related to React contexts or components like `Meta`, ensure you're importing from the correct package:

- Components like `Links`, `Meta`, `Outlet`, etc. should be imported from `react-router-dom`, not `react-router`
- If you see errors about missing context providers, check your imports and ensure all required packages are installed

#### Dependency Issues

If you encounter dependency-related errors:

```sh
# Clean node_modules and reinstall
rm -rf node_modules .vite
pnpm install
```

## Additional Context

Refer to `./docs` for additional context...
