/**
 * Verify Organization Script
 *
 * This script helps verify that you're logged in with the correct organization
 * (should be Clayton Property Management) before creating settlements.
 *
 * Run this script with: npx tsx scripts/find-organization.ts
 */

import { Effect } from 'effect';
import * as HttpApiClient from "@effect/platform/HttpApiClient";
import * as FetchHttpClient from "@effect/platform/FetchHttpClient";
import * as Layer from "effect/Layer";
import { Api } from '../app/api/api';

// Create API client with base URL
const ApiClient = HttpApiClient.make(Api, {
  baseUrl: 'http://localhost:1234',
}).pipe(
  Effect.provide(FetchHttpClient.layer),
);

// Function to verify the organization
const verifyOrganization = async () => {
  try {
    console.log('Verifying your current organization...');

    // Use the API client to get the current user's organization
    const result = await Effect.runPromise(
      Effect.gen(function* () {
        return yield* ApiClient.pipe(
          Effect.flatMap((client) => client.health.me())
        );
      })
    );

    console.log('Current user organization information:');
    console.log(JSON.stringify(result, null, 2));

    // If we have the organization information, check if it's Clayton Property Management
    if (result && result.organization) {
      console.log('\nYou are currently logged in with organization:');
      console.log(`Name: ${result.organization.name}`);
      console.log(`ID: ${result.organization.id}`);

      if (result.organization.name.includes('Clayton')) {
        console.log('\n✅ You appear to be logged in with Clayton Property Management.');
        console.log('You can now run the create-settlements.ts script to add seed data.');
      } else {
        console.log('\n⚠️ You do not appear to be logged in with Clayton Property Management.');
        console.log('Please log in with the correct organization before creating settlements.');
      }
    } else {
      console.log('\n❌ No organization found. Make sure you are logged in to the application.');
    }
  } catch (error) {
    console.error('Error verifying organization:', error);
  }
};

// Run the function
verifyOrganization();
