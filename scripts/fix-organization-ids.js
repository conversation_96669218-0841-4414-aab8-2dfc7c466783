// Script to fix organization IDs in settlements table
// This script updates all settlements to use the Clerk organization ID instead of the internal organization ID

const { drizzle } = require('drizzle-orm/d1');
const { eq } = require('drizzle-orm');
const { settlementsTable, orgsTable } = require('../db/schema');

async function fixOrganizationIds(db) {
  console.log('Starting organization ID fix...');
  
  // Get all organizations
  const organizations = await db.select().from(orgsTable);
  console.log(`Found ${organizations.length} organizations`);
  
  // For each organization, update settlements
  for (const org of organizations) {
    console.log(`Processing organization: ${org.name} (${org.uid})`);
    console.log(`Internal ID: ${org.uid}, Clerk ID: ${org.clerk_id}`);
    
    // Count settlements with internal ID
    const settlementsWithInternalId = await db
      .select({ count: db.fn.count() })
      .from(settlementsTable)
      .where(eq(settlementsTable.organization_id, org.uid));
    
    const count = settlementsWithInternalId[0]?.count || 0;
    console.log(`Found ${count} settlements with internal organization ID`);
    
    if (count > 0) {
      // Update settlements to use Clerk ID
      const result = await db
        .update(settlementsTable)
        .set({ organization_id: org.clerk_id })
        .where(eq(settlementsTable.organization_id, org.uid));
      
      console.log(`Updated ${count} settlements for ${org.name}`);
    }
  }
  
  console.log('Organization ID fix completed');
}

module.exports = { fixOrganizationIds };
