# TitleSync Scripts

This directory contains utility scripts for the TitleSync application.

## Organization Management Scripts

### Update Organization Slug

The `update-org-slug.js` script updates an organization's slug in the database to match what's in Clerk.

#### Background

When an organization is created in Clerk, it gets a slug that might include a numeric ID at the end (e.g., `clayton-property-management-org_2YXnZXXXXXXXXXXXXXXXXXXX`). If you update the slug in <PERSON> to remove this ID (e.g., to `clayton-property-management`), the change isn't automatically propagated to the database.

This script updates the organization slug in the database to match what's in Clerk.

#### Usage

Before running the script, make sure to update the following variables in the script:

```javascript
const oldSlug = 'clayton-property-management-org_2YXnZXXXXXXXXXXXXXXXXXXX'; // Replace with the actual old slug
const newSlug = 'clayton-property-management'; // Replace with the actual new slug
const orgName = 'Clayton Property Management'; // Replace with the actual organization name
```

Then run the script using Wrangler:

```bash
npx wrangler d1 execute titlesync --file=./scripts/update-org-slug.js
```

This will update the organization slug in the production database.

#### Verification

After running the script, you can verify that the organization slug has been updated by checking the logs or by querying the database:

```bash
npx wrangler d1 execute titlesync --command="SELECT * FROM organizations WHERE slug = 'clayton-property-management';"
```

## Seed Data Scripts

### Generate SQL for Test Settlements

This script generates SQL statements to insert test settlements for Clayton Property Management.

```sh
npx tsx scripts/generate-sql.ts > seed-data.sql
```

This will create a SQL file with INSERT statements for 10 test settlements. You can then run this SQL in the Cloudflare D1 console or using wrangler.

**IMPORTANT**: You need to replace the `CLAYTON_ORG_ID` in the script with the actual organization ID for Clayton Property Management.

### Create Settlements (Alternative Approach)

This script attempts to create settlements using the application's API, but requires authentication.

```sh
npx tsx scripts/create-settlements.ts
```

**IMPORTANT**: You must be logged in to the application as a user in the Clayton Property Management organization for this script to work correctly. The script will use your authentication to create settlements for your current organization.

### Verify Organization

If you want to verify that you're logged in with the correct organization before creating settlements, you can use:

```sh
npx tsx scripts/find-organization.ts
```

This will show information about your currently authenticated organization.

## Usage Instructions

### Option 1: Using SQL (Recommended)

1. Generate the SQL statements:
   ```sh
   npx tsx scripts/generate-sql.ts > seed-data.sql
   ```

2. Edit the `seed-data.sql` file to replace `CLAYTON_ORG_ID` with the actual organization ID for Clayton Property Management.

3. Run the SQL in the Cloudflare D1 console or using wrangler.

4. Refresh the settlements page in the application to see the newly created settlements.

### Option 2: Using the API (Requires Authentication)

1. Make sure the development server is running:
   ```sh
   pnpm dev
   ```

2. Log in to the application as a user in the Clayton Property Management organization.

3. In a separate terminal, verify that you're logged in with the correct organization:
   ```sh
   npx tsx scripts/find-organization.ts
   ```

4. If you're logged in with the correct organization, run the create-settlements script:
   ```sh
   npx tsx scripts/create-settlements.ts
   ```

5. Refresh the settlements page in the application to see the newly created settlements.

## Notes

- These scripts use the application's API to create settlements, so they require you to be logged in with the correct permissions.
- The settlements are created with random data, including property addresses, HOA names, and expenses.
- All settlements are created with the "pending" payment status by default (this is handled by the API).
- The script creates settlements for your currently authenticated organization, which should be Clayton Property Management.
- Each time you run the script, it will create 10 new settlements, so you can run it multiple times if you need more data.
- All settlements created by this script are flagged as test data with "[TEST]" in their names, making them easy to identify.
