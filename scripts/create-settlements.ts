/**
 * Create Settlements Script
 *
 * This script creates 10 fake settlements for the currently authenticated organization
 * (should be Clayton Property Management) using the application's API.
 *
 * IMPORTANT: You must be logged in to the application as a user in the Clayton Property Management
 * organization for this script to work correctly. The script will use your authentication
 * to create settlements for your current organization.
 *
 * Run this script with: npx tsx scripts/create-settlements.ts
 */

import { Effect } from 'effect';
import * as HttpApiClient from "@effect/platform/HttpApiClient";
import * as FetchHttpClient from "@effect/platform/FetchHttpClient";
import * as Layer from "effect/Layer";
import { Api } from '../app/api/api';

// Create API client with base URL
const ApiClient = HttpApiClient.make(Api, {
  baseUrl: 'http://localhost:1234',
}).pipe(
  Effect.provide(FetchHttpClient.layer),
);

// Sample HOA names
const HOA_NAMES = [
  'Oakridge Community Association',
  'Pinecrest Homeowners Association',
  'Riverside Estates HOA',
  'Meadowbrook Community',
  'Sunset Hills Association',
  'Lakeside Village HOA',
  'Cedar Heights Community',
  'Willow Creek Estates',
  'Maple Grove Association',
  'Evergreen Valley HOA'
];

// Sample property addresses
const PROPERTY_ADDRESSES = [
  '123 Main Street, Austin, TX 78701',
  '456 Oak Avenue, Austin, TX 78702',
  '789 Pine Boulevard, Austin, TX 78703',
  '321 Maple Drive, Austin, TX 78704',
  '654 Cedar Lane, Austin, TX 78705',
  '987 Elm Street, Austin, TX 78712',
  '246 Birch Road, Austin, TX 78721',
  '135 Willow Way, Austin, TX 78722',
  '864 Spruce Court, Austin, TX 78723',
  '579 Redwood Circle, Austin, TX 78724'
];

// Sample expense categories
const EXPENSE_CATEGORIES = [
  'dues',
  'special-assessment',
  'move-in-out-fee',
  'transfer-fee',
  'working-capital',
  'other'
];

// Helper function to generate a random number between min and max (inclusive)
const randomInt = (min: number, max: number) => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

// Helper function to generate random expenses
const generateExpenses = (count: number) => {
  const expenses = [];

  for (let i = 0; i < count; i++) {
    const category = EXPENSE_CATEGORIES[randomInt(0, EXPENSE_CATEGORIES.length - 1)];
    const amount = randomInt(50, 1000);

    const expense: any = {
      category,
      amount
    };

    // Add subcategory for 'other' category
    if (category === 'other') {
      expense.subcategory = ['Cleaning fee', 'Documentation fee', 'Processing fee', 'Administrative fee'][randomInt(0, 3)];
    }

    // Add month and year for 'dues' category
    if (category === 'dues') {
      const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
      expense.month = months[randomInt(0, 11)];
      expense.year = String(new Date().getFullYear());
    }

    expenses.push(expense);
  }

  return expenses;
};

// Generate a settlement payload
const generateSettlementPayload = (index: number) => {
  const propertyAddress = PROPERTY_ADDRESSES[index % PROPERTY_ADDRESSES.length];
  const hoaName = HOA_NAMES[randomInt(0, HOA_NAMES.length - 1)];

  // Generate more seller expenses than buyer expenses (typical in real settlements)
  const sellerExpenses = generateExpenses(randomInt(2, 4));
  const buyerExpenses = generateExpenses(randomInt(1, 2));

  // Calculate total amount
  const totalAmount = [...sellerExpenses, ...buyerExpenses].reduce((sum, expense) => sum + expense.amount, 0);

  // Generate a realistic buyer email
  const firstNames = ['john', 'jane', 'robert', 'sarah', 'michael', 'emily', 'david', 'lisa'];
  const lastNames = ['smith', 'johnson', 'williams', 'brown', 'jones', 'miller', 'davis', 'garcia'];
  const domains = ['gmail.com', 'yahoo.com', 'outlook.com', 'icloud.com', 'hotmail.com'];

  const firstName = firstNames[randomInt(0, firstNames.length - 1)];
  const lastName = lastNames[randomInt(0, lastNames.length - 1)];
  const domain = domains[randomInt(0, domains.length - 1)];
  const buyerEmail = `${firstName}.${lastName}@${domain}`;

  // Flag this as test data by adding "[TEST]" to the name
  return {
    name: `[TEST] Settlement for ${propertyAddress.split(',')[0]}`,
    property_address: propertyAddress,
    unit: randomInt(1, 5) > 3 ? `Unit ${randomInt(100, 999)}` : undefined,
    hoa_name: hoaName,
    buyer_email: buyerEmail,
    seller_expenses: sellerExpenses,
    buyer_expenses: buyerExpenses,
    total_amount: totalAmount
  };
};

// Main function to create test settlements
const createSettlements = async () => {
  try {
    console.log('Starting to create TEST settlements for Clayton Property Management...');
    console.log('These settlements will be flagged as test data with "[TEST]" in their names.');

    // Create 10 test settlements
    for (let i = 0; i < 10; i++) {
      const settlementPayload = generateSettlementPayload(i);
      console.log(`Creating test settlement ${i + 1}/10: ${settlementPayload.name}`);

      // Use the API client to create the settlement
      const result = await Effect.runPromise(
        Effect.gen(function* () {
          return yield* ApiClient.pipe(
            Effect.flatMap((client) => client.settlements.create({
              payload: settlementPayload
            }))
          );
        })
      );

      console.log(`Test settlement created with ID: ${result.id}`);
    }

    console.log('All test settlements created successfully!');
    console.log('You can now view these test settlements in the Property Manager Dashboard.');
  } catch (error) {
    console.error('Error creating test settlements:', error);
  }
};

// Run the function
createSettlements();
