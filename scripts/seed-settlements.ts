/**
 * Seed Settlements Script (DEPRECATED)
 *
 * NOTE: This script is deprecated. Please use create-settlements.ts instead.
 *
 * This script was intended to create settlements directly in the database,
 * but this approach doesn't work well with Cloudflare Workers and D1.
 *
 * Instead, use the create-settlements.ts script which uses the application's API
 * to create settlements while authenticated as a user in the Clayton Property Management
 * organization.
 */

import { Effect } from 'effect';
import { typeid } from 'typeid-js';
import { settlementsTable } from '../db/schema';
import { drizzle } from 'drizzle-orm/d1';

// Sample HOA names
const HOA_NAMES = [
  'Oakridge Community Association',
  'Pinecrest Homeowners Association',
  'Riverside Estates HOA',
  'Meadowbrook Community',
  'Sunset Hills Association',
  'Lakeside Village HOA',
  'Cedar Heights Community',
  'Willow Creek Estates',
  'Maple Grove Association',
  'Evergreen Valley HOA'
];

// Sample property addresses
const PROPERTY_ADDRESSES = [
  '123 Main Street',
  '456 Oak Avenue',
  '789 Pine Boulevard',
  '321 Maple Drive',
  '654 Cedar Lane',
  '987 Elm Street',
  '246 Birch Road',
  '135 Willow Way',
  '864 Spruce Court',
  '579 Redwood Circle',
  '753 Aspen Place',
  '951 Sycamore Avenue',
  '357 Poplar Street',
  '159 Chestnut Lane',
  '852 Walnut Drive'
];

// Sample payment statuses
const PAYMENT_STATUSES = ['pending', 'paid', 'overdue', 'processing'];

// Sample expense categories
const EXPENSE_CATEGORIES = [
  'dues',
  'special-assessment',
  'move-in-out-fee',
  'transfer-fee',
  'working-capital',
  'other'
];

// Helper function to generate a random number between min and max (inclusive)
const randomInt = (min: number, max: number) => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

// Helper function to generate a random date within the last 6 months
const randomDate = () => {
  const now = new Date();
  const sixMonthsAgo = new Date();
  sixMonthsAgo.setMonth(now.getMonth() - 6);

  const randomTimestamp = sixMonthsAgo.getTime() + Math.random() * (now.getTime() - sixMonthsAgo.getTime());
  return new Date(randomTimestamp).toISOString();
};

// Helper function to generate random expenses
const generateExpenses = (count: number) => {
  const expenses = [];

  for (let i = 0; i < count; i++) {
    const category = EXPENSE_CATEGORIES[randomInt(0, EXPENSE_CATEGORIES.length - 1)];
    const amount = randomInt(50, 1000);

    const expense: any = {
      category,
      amount
    };

    // Add subcategory for 'other' category
    if (category === 'other') {
      expense.subcategory = ['Cleaning fee', 'Documentation fee', 'Processing fee', 'Administrative fee'][randomInt(0, 3)];
    }

    // Add month and year for 'dues' category
    if (category === 'dues') {
      const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
      expense.month = months[randomInt(0, 11)];
      expense.year = String(new Date().getFullYear());
    }

    expenses.push(expense);
  }

  return expenses;
};

// Generate a settlement
const generateSettlement = () => {
  const propertyAddress = PROPERTY_ADDRESSES[randomInt(0, PROPERTY_ADDRESSES.length - 1)];
  const hoaName = HOA_NAMES[randomInt(0, HOA_NAMES.length - 1)];
  const paymentStatus = PAYMENT_STATUSES[randomInt(0, PAYMENT_STATUSES.length - 1)];

  const sellerExpenses = generateExpenses(randomInt(1, 3));
  const buyerExpenses = generateExpenses(randomInt(1, 3));

  // Calculate total amount
  const totalAmount = [...sellerExpenses, ...buyerExpenses].reduce((sum, expense) => sum + expense.amount, 0);

  const createdAt = randomDate();

  return {
    uid: typeid('settlement').toString(),
    name: `Settlement for ${propertyAddress}`,
    organization_id: CLAYTON_ORG_ID,
    property_address: propertyAddress,
    unit: randomInt(1, 5) > 3 ? `Unit ${randomInt(100, 999)}` : null,
    hoa_name: hoaName,
    buyer_email: `buyer${randomInt(100, 999)}@example.com`,
    warranty_deed_key: null,
    amount: null,
    payment_status: paymentStatus,
    seller_expenses: JSON.stringify(sellerExpenses),
    buyer_expenses: JSON.stringify(buyerExpenses),
    total_amount: totalAmount,
    created_at: createdAt,
    updated_at: createdAt
  };
};

// Main function to seed the database (DEPRECATED)
const seedSettlements = async () => {
  try {
    console.log('This script is deprecated. Please use create-settlements.ts instead.');
    console.log('');
    console.log('To create settlements:');
    console.log('1. Make sure the development server is running: pnpm dev');
    console.log('2. Log in to the application as a user in the Clayton Property Management organization');
    console.log('3. Run: npx tsx scripts/create-settlements.ts');

    return;
  } catch (error) {
    console.error('Error:', error);
  }
};

// Run the function
seedSettlements();
