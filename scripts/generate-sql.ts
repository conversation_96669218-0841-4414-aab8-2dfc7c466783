/**
 * Generate SQL Script
 * 
 * This script generates SQL statements to insert test settlements for Clayton Property Management.
 * Run this script with: npx tsx scripts/generate-sql.ts > seed-data.sql
 */

import { typeid } from 'typeid-js';

// Clayton Property Management organization ID
// Note: You'll need to replace this with the actual organization ID
const CLAYTON_ORG_ID = 'org_2YXnZXXXXXXXXXXXXXXXXXXX'; // Replace with actual ID

// Sample HOA names
const HOA_NAMES = [
  'Oakridge Community Association',
  'Pinecrest Homeowners Association',
  'Riverside Estates HOA',
  'Meadowbrook Community',
  'Sunset Hills Association',
  'Lakeside Village HOA',
  'Cedar Heights Community',
  'Willow Creek Estates',
  'Maple Grove Association',
  'Evergreen Valley HOA'
];

// Sample property addresses
const PROPERTY_ADDRESSES = [
  '123 Main Street, Austin, TX 78701',
  '456 Oak Avenue, Austin, TX 78702',
  '789 Pine Boulevard, Austin, TX 78703',
  '321 Maple Drive, Austin, TX 78704',
  '654 Cedar Lane, Austin, TX 78705',
  '987 Elm Street, Austin, TX 78712',
  '246 Birch Road, Austin, TX 78721',
  '135 Willow Way, Austin, TX 78722',
  '864 Spruce Court, Austin, TX 78723',
  '579 Redwood Circle, Austin, TX 78724'
];

// Sample payment statuses
const PAYMENT_STATUSES = ['pending', 'paid', 'overdue', 'processing'];

// Sample expense categories
const EXPENSE_CATEGORIES = [
  'dues',
  'special-assessment',
  'move-in-out-fee',
  'transfer-fee',
  'working-capital',
  'other'
];

// Helper function to generate a random number between min and max (inclusive)
const randomInt = (min: number, max: number) => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

// Helper function to generate a random date within the last 6 months
const randomDate = () => {
  const now = new Date();
  const sixMonthsAgo = new Date();
  sixMonthsAgo.setMonth(now.getMonth() - 6);
  
  const randomTimestamp = sixMonthsAgo.getTime() + Math.random() * (now.getTime() - sixMonthsAgo.getTime());
  return new Date(randomTimestamp).toISOString();
};

// Helper function to generate random expenses
const generateExpenses = (count: number) => {
  const expenses = [];
  
  for (let i = 0; i < count; i++) {
    const category = EXPENSE_CATEGORIES[randomInt(0, EXPENSE_CATEGORIES.length - 1)];
    const amount = randomInt(50, 1000);
    
    const expense: any = {
      category,
      amount
    };
    
    // Add subcategory for 'other' category
    if (category === 'other') {
      expense.subcategory = ['Cleaning fee', 'Documentation fee', 'Processing fee', 'Administrative fee'][randomInt(0, 3)];
    }
    
    // Add month and year for 'dues' category
    if (category === 'dues') {
      const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
      expense.month = months[randomInt(0, 11)];
      expense.year = String(new Date().getFullYear());
    }
    
    expenses.push(expense);
  }
  
  return expenses;
};

// Generate a settlement
const generateSettlement = (index: number) => {
  const propertyAddress = PROPERTY_ADDRESSES[index % PROPERTY_ADDRESSES.length];
  const hoaName = HOA_NAMES[randomInt(0, HOA_NAMES.length - 1)];
  const paymentStatus = PAYMENT_STATUSES[randomInt(0, PAYMENT_STATUSES.length - 1)];
  
  // Generate more seller expenses than buyer expenses (typical in real settlements)
  const sellerExpenses = generateExpenses(randomInt(2, 4));
  const buyerExpenses = generateExpenses(randomInt(1, 2));
  
  // Calculate total amount
  const totalAmount = [...sellerExpenses, ...buyerExpenses].reduce((sum, expense) => sum + expense.amount, 0);
  
  // Generate a realistic buyer email
  const firstNames = ['john', 'jane', 'robert', 'sarah', 'michael', 'emily', 'david', 'lisa'];
  const lastNames = ['smith', 'johnson', 'williams', 'brown', 'jones', 'miller', 'davis', 'garcia'];
  const domains = ['gmail.com', 'yahoo.com', 'outlook.com', 'icloud.com', 'hotmail.com'];
  
  const firstName = firstNames[randomInt(0, firstNames.length - 1)];
  const lastName = lastNames[randomInt(0, lastNames.length - 1)];
  const domain = domains[randomInt(0, domains.length - 1)];
  const buyerEmail = `${firstName}.${lastName}@${domain}`;
  
  const createdAt = randomDate();
  
  return {
    uid: typeid('settlement').toString(),
    name: `[TEST] Settlement for ${propertyAddress.split(',')[0]}`,
    organization_id: CLAYTON_ORG_ID,
    property_address: propertyAddress,
    unit: randomInt(1, 5) > 3 ? `Unit ${randomInt(100, 999)}` : null,
    hoa_name: hoaName,
    buyer_email: buyerEmail,
    warranty_deed_key: null,
    amount: null,
    payment_status: paymentStatus,
    seller_expenses: JSON.stringify(sellerExpenses),
    buyer_expenses: JSON.stringify(buyerExpenses),
    total_amount: totalAmount,
    created_at: createdAt,
    updated_at: createdAt
  };
};

// Main function to generate SQL
const generateSQL = () => {
  try {
    console.log('-- SQL to insert test settlements for Clayton Property Management');
    console.log('-- Replace CLAYTON_ORG_ID with the actual organization ID');
    console.log('-- Run this SQL in the Cloudflare D1 console or using wrangler');
    console.log();
    
    // Create 10 settlements
    for (let i = 0; i < 10; i++) {
      const settlement = generateSettlement(i);
      
      // Generate SQL INSERT statement
      console.log(`-- Settlement ${i + 1}: ${settlement.name}`);
      console.log(`INSERT INTO settlements (
  uid,
  name,
  organization_id,
  property_address,
  unit,
  hoa_name,
  buyer_email,
  warranty_deed_key,
  amount,
  payment_status,
  seller_expenses,
  buyer_expenses,
  total_amount,
  created_at,
  updated_at
) VALUES (
  '${settlement.uid}',
  '${settlement.name.replace(/'/g, "''")}',
  '${settlement.organization_id}',
  '${settlement.property_address.replace(/'/g, "''")}',
  ${settlement.unit ? `'${settlement.unit}'` : 'NULL'},
  '${settlement.hoa_name.replace(/'/g, "''")}',
  '${settlement.buyer_email}',
  ${settlement.warranty_deed_key ? `'${settlement.warranty_deed_key}'` : 'NULL'},
  ${settlement.amount ? settlement.amount : 'NULL'},
  '${settlement.payment_status}',
  '${settlement.seller_expenses.replace(/'/g, "''")}',
  '${settlement.buyer_expenses.replace(/'/g, "''")}',
  ${settlement.total_amount},
  '${settlement.created_at}',
  '${settlement.updated_at}'
);`);
      console.log();
    }
    
    console.log('-- End of SQL');
  } catch (error) {
    console.error('Error generating SQL:', error);
  }
};

// Run the function
generateSQL();
