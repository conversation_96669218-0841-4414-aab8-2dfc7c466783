// Run the organization ID fix script
const { fixOrganizationIds } = require('./fix-organization-ids');
const { drizzle } = require('drizzle-orm/d1');

// This script is meant to be run in a Cloudflare Workers environment
// where the database is available as a Durable Object

addEventListener('fetch', (event) => {
  event.respondWith(handleRequest(event.request));
});

async function handleRequest(request) {
  // Get the database from the Durable Object
  const id = env.MyDurableObject.idFromName('default');
  const obj = env.MyDurableObject.get(id);
  
  // Call the fix function
  try {
    await fixOrganizationIds(obj.db);
    return new Response('Organization IDs fixed successfully', { status: 200 });
  } catch (error) {
    console.error('Error fixing organization IDs:', error);
    return new Response(`Error: ${error.message}`, { status: 500 });
  }
}
