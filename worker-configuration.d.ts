// Generated by Wrang<PERSON> by running `wrangler types`

interface Env {
	VALUE_FROM_CLOUDFLARE: "Hello from Cloudflare";
	OPENAI_API_KEY: string;
	VITE_YOUTUBE_CLIENT_ID: string;
	VITE_YOUTUBE_REDIRECT_URI: string;
	VITE_CLERK_PUBLISHABLE_KEY: string;
	CLERK_SECRET_KEY: string;
	VITE_STRIPE_PUBLISHABLE_KEY: string;
	STRIPE_SECRET_KEY: string;
	STRIPE_WEBHOOK_SECRET: string;
	MyAgent: DurableObjectNamespace<import("./workers/app").MyAgent>;
	MyDurableObject: DurableObjectNamespace<import("./workers/app").MyDurableObject>;
	BUCKET: R2Bucket;
}

declare module "*.sql" {
  const value: string;
  export default value;
}
