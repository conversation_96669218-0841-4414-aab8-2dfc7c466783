# Clerk Integration Guide

This document outlines how <PERSON><PERSON><PERSON> integrates with <PERSON> for authentication and organization management.

## Current Implementation: Authentication Flow Sync

Currently, TitleSync synchronizes organization data between Clerk and our database during the authentication flow.

### How It Works

1. When a user authenticates, we fetch the latest user and organization data from Clerk.
2. We check if the user exists in our database. If not, we create a new user record.
3. We check if the organization exists in our database:
   - If it doesn't exist, we create a new organization record with data from <PERSON>.
   - If it exists, we check if the data (name, slug) has changed in <PERSON> and update our database accordingly.

This approach ensures that organization data stays in sync whenever a user authenticates.

### Implementation Details

The synchronization logic is implemented in `app/api/authentication.server.ts`:

```typescript
// Fetch the latest organization data from Clerk
const clerkOrg = yield* Effect.promise(() =>
  clerk.organizations.getOrganization({
    organizationId: auth.orgId as string,
  }),
);

// Check if the organization exists in our database
const existingOrgs = yield* Effect.promise(() =>
  db
    .select()
    .from(orgsTable)
    .where(eq(orgsTable.clerk_id, auth.orgId as string))
    .limit(1),
);

if (existingOrgs.length === 0) {
  // Organization doesn't exist, create it
  yield* Effect.promise(() =>
    db.insert(orgsTable).values({
      uid: typeid("org").toString(),
      clerk_id: auth.orgId as string,
      name: clerkOrg.name || "Unknown Organization",
      slug: clerkOrg.slug || "unknown",
    }),
  );
} else {
  // Organization exists, check if it needs to be updated
  const existingOrg = existingOrgs[0];
  const needsUpdate =
    existingOrg.name !== clerkOrg.name ||
    existingOrg.slug !== clerkOrg.slug;

  if (needsUpdate) {
    // Update the organization data
    yield* Effect.promise(() =>
      db.update(orgsTable)
        .set({
          name: clerkOrg.name || "Unknown Organization",
          slug: clerkOrg.slug || "unknown",
        })
        .where(eq(orgsTable.clerk_id, auth.orgId as string)),
    );
  }
}
```

### Limitations

- Updates only happen when users authenticate
- Changes made in Clerk may not be reflected immediately in our application
- If a user doesn't authenticate for a long time, our database may become out of sync

## Future Implementation: Webhooks

For a more robust integration, we plan to implement webhooks to keep our database in sync with Clerk in real-time.

### How Webhooks Work

1. Clerk provides webhooks that notify your application when events occur (user created, user updated, organization created, organization updated, etc.)
2. Your application sets up webhook endpoints to receive these notifications
3. When an event occurs in Clerk, it sends a POST request to your webhook endpoint with the event data
4. Your application processes the webhook and updates your database accordingly

### Implementation Plan

1. **Create Webhook Endpoints**:
   - Create API endpoints to handle Clerk webhook events
   - Implement handlers for organization-related events (created, updated, deleted)
   - Secure the endpoints with webhook signature verification

2. **Register Webhooks with Clerk**:
   - Configure Clerk to send webhooks to our endpoints
   - Select the relevant events to listen for (organization.created, organization.updated, etc.)

3. **Process Webhook Events**:
   - Validate the webhook signature
   - Extract the organization data from the event
   - Update our database accordingly

### Example Webhook Handler

```typescript
// Example webhook handler for organization events
app.post('/api/webhooks/clerk', async (req, res) => {
  // Verify the webhook signature
  const signature = req.headers['clerk-signature'];
  if (!verifyClerkWebhookSignature(signature, req.body, process.env.CLERK_WEBHOOK_SECRET)) {
    return res.status(401).send('Invalid signature');
  }

  const { type, data } = req.body;

  // Handle organization events
  if (type === 'organization.created' || type === 'organization.updated') {
    const { id, name, slug } = data;
    
    // Update or create the organization in our database
    await db.transaction(async (tx) => {
      const existingOrg = await tx
        .select()
        .from(orgsTable)
        .where(eq(orgsTable.clerk_id, id))
        .limit(1);
      
      if (existingOrg.length === 0) {
        // Create new organization
        await tx.insert(orgsTable).values({
          uid: typeid("org").toString(),
          clerk_id: id,
          name: name || "Unknown Organization",
          slug: slug || "unknown",
        });
      } else {
        // Update existing organization
        await tx.update(orgsTable)
          .set({
            name: name || "Unknown Organization",
            slug: slug || "unknown",
          })
          .where(eq(orgsTable.clerk_id, id));
      }
    });
  }

  res.status(200).send('Webhook processed');
});
```

### Benefits of Webhooks

- Real-time updates: Your database is updated immediately when changes occur in Clerk
- Comprehensive: Captures all changes, not just those that happen during authentication
- Reliable: Doesn't depend on user actions to trigger updates

## References

- [Clerk Webhooks Documentation](https://clerk.com/docs/integrations/webhooks)
- [Clerk Organizations API](https://clerk.com/docs/references/javascript/organization)
