# Styling Clerk Authentication Pages

This document provides instructions for styling the Clerk authentication pages to match the TitleSync application design.

## Current Issue

The Clerk authentication page is hosted on <PERSON>'s domain (`improved-caiman-90.accounts.dev`) rather than on our application domain. This means that styling changes in our application code won't affect the Clerk-hosted authentication page.

## Option 1: Configure Appearance via Clerk Dashboard

Since the authentication page is hosted on <PERSON>'s domain, we need to configure the appearance through Clerk's Dashboard:

1. **Log in to Clerk Dashboard**:
   - Go to [dashboard.clerk.com](https://dashboard.clerk.com/)
   - Sign in with your Clerk account

2. **Navigate to Appearance Settings**:
   - Select your application
   - Go to "Customization" in the sidebar
   - Click on "Appearance"

3. **Customize the Authentication Page**:
   - In the Appearance editor, you can customize:
     - Colors (primary, background, text, etc.)
     - Typography
     - Border radius
     - Logo
     - Layout
     - Button styles
     - Form field styles

4. **Recommended Styling Settings**:
   - Primary color: `#10b981` (green-600)
   - Background: Gradient from `#3b82f6` to `#10b981`
   - Logo: Upload the TitleSync logo (`/public/titlesync-logo.png`)
   - Border radius: `0.75rem`
   - Card background: White
   - Text color: `#1e293b`

5. **Save Changes**:
   - Click "Save" to apply your changes
   - The changes will be immediately reflected on your authentication pages

## Option 2: Custom Authentication Flow

For complete control over the authentication UI, you can use Clerk's components within your own application instead of redirecting to Clerk's hosted pages:

1. **Create a Custom Sign-In Page**:

```tsx
// app/routes/auth/signin.tsx
import { SignIn } from "@clerk/react-router";
import { Container } from "~/components/container";

export default function SignInPage() {
  return (
    <Container variant="property-manager">
      <div className="flex justify-center py-8">
        <SignIn 
          routing="path" 
          path="/auth/signin" 
          signUpUrl="/auth/signup"
          appearance={{
            elements: {
              rootBox: {
                boxShadow: "none",
                width: "100%",
                maxWidth: "400px",
              }
            }
          }}
        />
      </div>
    </Container>
  );
}
```

2. **Create a Custom Sign-Up Page**:

```tsx
// app/routes/auth/signup.tsx
import { SignUp } from "@clerk/react-router";
import { Container } from "~/components/container";

export default function SignUpPage() {
  return (
    <Container variant="property-manager">
      <div className="flex justify-center py-8">
        <SignUp 
          routing="path" 
          path="/auth/signup" 
          signInUrl="/auth/signin"
          appearance={{
            elements: {
              rootBox: {
                boxShadow: "none",
                width: "100%",
                maxWidth: "400px",
              }
            }
          }}
        />
      </div>
    </Container>
  );
}
```

3. **Configure Clerk to Use Your Routes**:
   - In your `ClerkProvider` in `app/root.tsx`, set the paths for sign-in and sign-up:

```tsx
<ClerkProvider
  appearance={{
    variables: {
      colorPrimary: "#10b981",
      colorBackground: "#3b82f6",
      colorText: "#1e293b",
      colorDanger: "#ef4444",
      colorSuccess: "#10b981",
      borderRadius: "0.75rem",
    },
    // Other appearance settings...
  }}
  routing="path"
  signInUrl="/auth/signin"
  signUpUrl="/auth/signup"
  loaderData={loaderData}
>
  <Outlet />
</ClerkProvider>
```

4. **Update Routes Configuration**:
   - Add the new routes to your `app/routes.ts` file:

```tsx
export default [
  // Existing routes...
  
  // Auth routes
  route("auth/signin", "routes/auth/signin.tsx"),
  route("auth/signup", "routes/auth/signup.tsx"),
  
  // Other routes...
];
```

## Recommended Approach

Option 2 (Custom Authentication Flow) provides more control over the styling and user experience, as it keeps users within your application domain throughout the authentication process. However, it requires more implementation work.

Option 1 (Clerk Dashboard Configuration) is quicker to implement but provides less control over the exact styling and behavior.

## Current Configuration

The current Clerk appearance configuration in `app/root.tsx` is:

```tsx
<ClerkProvider
  appearance={{
    variables: {
      colorPrimary: "#10b981", /* Green-600 for buttons */
      colorBackground: "#3b82f6", /* Blue-500 for background */
      colorText: "#1e293b",
      colorDanger: "#ef4444",
      colorSuccess: "#10b981",
      borderRadius: "0.75rem",
    },
    layout: {
      logoPlacement: "inside",
      showOptionalFields: false,
      socialButtonsVariant: "iconButton",
      logoImageUrl: "/titlesync-logo.png" // Use TitleSync logo
    },
    elements: {
      rootBox: {
        backgroundColor: "linear-gradient(to bottom, #3b82f6, #10b981)",
        minHeight: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      },
      card: {
        boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
        borderRadius: "0.75rem",
        maxWidth: "400px",
        width: "100%",
        padding: "1.5rem",
        border: "1px solid rgba(229, 231, 235, 1)",
      },
      headerTitle: {
        fontSize: "1.5rem",
        fontWeight: "600",
        textAlign: "center",
        marginBottom: "0.5rem",
      },
      headerSubtitle: {
        fontSize: "0.875rem",
        color: "#6B7280",
        textAlign: "center",
        marginBottom: "1.5rem",
      },
      formButtonPrimary: {
        backgroundColor: "#10b981",
        borderRadius: "0.75rem",
        fontSize: "1rem",
        padding: "0.75rem 1rem",
        fontWeight: "500",
        width: "100%",
        boxShadow: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
        marginTop: "0.5rem",
      },
      formFieldLabel: {
        fontSize: "0.875rem",
        fontWeight: "500",
        color: "#374151",
        marginBottom: "0.5rem",
      },
      formFieldInput: {
        borderRadius: "0.5rem",
        padding: "0.75rem 1rem",
        fontSize: "1rem",
        borderColor: "#E5E7EB",
        boxShadow: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
      },
      footerActionLink: {
        color: "#3B82F6",
        fontWeight: "500",
      },
      footer: {
        marginTop: "1.5rem",
        textAlign: "center",
      },
      logoImage: {
        width: "auto",
        height: "40px",
        margin: "0 auto 1rem auto",
        display: "block",
      },
      socialButtonsIconButton: {
        border: "1px solid #E5E7EB",
        borderRadius: "0.5rem",
        boxShadow: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
      },
      dividerLine: {
        backgroundColor: "#E5E7EB",
      },
      dividerText: {
        color: "#6B7280",
        fontSize: "0.875rem",
      },
      identityPreviewEditButton: {
        color: "#3B82F6",
      },
      formFieldAction: {
        color: "#3B82F6",
      },
      alert: {
        borderRadius: "0.5rem",
        fontSize: "0.875rem",
      }
    }
  }}
  loaderData={loaderData}
  signUpFallbackRedirectUrl="/"
  signInFallbackRedirectUrl="/"
>
  <Outlet />
</ClerkProvider>
```

This configuration can be used as a reference when setting up the appearance in the Clerk Dashboard or when implementing the custom authentication flow.
