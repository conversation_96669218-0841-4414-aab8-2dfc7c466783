# TitleSync: Settlement Automation Tool

## Product Requirement Document (PRD)

### Project Overview

**Project Name:** TitleSync (Settlement Automation Tool)

**Purpose:** Streamline the settlement process for property managers and title companies by automating payment allocation and document submission between buyers, sellers, and HOAs.

**Objectives:**

- Simplify and digitize the workflow for HOA settlement processes
- Enable clear and accurate allocation of payments between buyers and sellers
- Provide seamless integration with existing systems like Vantaca and title companies' platforms

### Problem Statement

**Problem Description:**  
<PERSON>, a property manager, struggles with a manual and unclear settlement process involving title companies. The current process is inefficient, leading to:

- Confusion about payment responsibilities and allocation of funds between buyers and sellers
- Piles of paper documentation
- Long delays between closing of a sale and alerting property management
- Poor experience for buyers when they move into their new home and the property manager doesn't know the sale closed

There is no centralized or automated solution for processing payments, uploading warranty deeds, and ensuring the settlement process is transparent and efficient for property managers.

**Target Audience:**

- Property managers handling HOA settlements
- Title companies involved in property transactions
- Buyers and sellers navigating the property purchase process

### Solution Overview

**Core Features:**

- Web app where title companies input buyer and seller payment details
- Automatic calculation and allocation of payments to buyer and seller accounts
- Electronic payment submission to property managers
- Upload functionality for warranty deeds and settlement statements
- Role-based access for property managers and title companies:
  - **Title Company Role:** Input payment information, send payments, and upload required documents
  - **Property Manager Role:** View submitted information, process payments (including HOA dues and transaction fees), and finalize settlements

**Feature Prioritization:**

_MVP Features:_

- Input fields for payment responsibilities (buyer and seller)
- Automatic allocation and account updates for buyers and sellers
- Ability to upload warranty deeds
- Role-based user access for title companies and property managers
- Issue electronic payment from title companies to property managers

_Future Features:_

- Full integration with Homewise and Zego for automated status letters and condo questionnaires
- Integration with Vantaca or other HOA software to generate a new owner (buyer) when a unit closes with the title company
- Detailed reporting and analytics for property managers
- Additional payment options and third-party integrations

### User Stories

1. As a title company, I want to input buyer and seller payment details so that I can ensure accurate allocation and compliance with settlement agreements.

2. As a property manager, I want to receive payments electronically and warranty deeds automatically, so that I can streamline the settlement process and reduce manual work.

3. As a title company, I want to submit payments and upload necessary documents, so that the property manager can finalize the settlement efficiently.

4. As a property manager, I want to process payments for HOA dues and transaction fees, so that the accounts are settled accurately.

5. As a property manager, I want to be alerted early in the process that a unit is being sold, so that I can prepare HOA software systems and admin processes to support a new owner.

### Technical Requirements

**Technology Stack:**

- **Frontend:** Vite / React / Shadcn / Tailwind / React Router / Tansack React Query / Tanstack Table / React Hook Forms / Monaco
- **Backend:** Effect / Cloudflare Workers / Jazz
- **Database:** Sqlite
- **Hosting:** Any host eventually, but for now cloudflare workers for serverless deployment

**Dependencies:**

- APIs for electronic payment gateways

### Success Metrics

**KPIs:**

- Reduction in manual errors in settlement calculations
- Time saved per settlement process (target: 50% reduction)
- Number of title companies and property managers adopting the platform

**User Feedback:**

- Regular surveys and feedback forms to measure satisfaction
- Metrics for user engagement and retention

### Timeline and Milestones

**Milestone 1: MVP design and development**

- Create core input fields and payment allocation logic
- Develop the warranty deed upload feature
- Implement role-based access for title companies and property managers

**Milestone 2: Integration with payment platforms**

- Enable electronic payments

**Milestone 3: Beta testing and user feedback**

- Test with select property managers and title companies
- Refine based on feedback

### Potential Risks

**Risk 1:** Resistance from title companies to adopt a new tool  
**Mitigation:** Offer free trials and demonstrations to showcase efficiency gains

**Risk 2:** Regulatory challenges around transaction fees  
**Mitigation:** Ensure compliance with local regulations and adapt fee structures as needed

### Additional Notes

- Regulatory compliance (e.g., transfer fees) should be considered in the design
- Scalability for future expansion should be a priority during development
